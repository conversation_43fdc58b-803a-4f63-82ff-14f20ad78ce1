

<?php $__env->startSection('title', 'إضافة هدية جديدة'); ?>
<?php $__env->startSection('page-title', 'إضافة هدية جديدة'); ?>

<?php $__env->startPush('styles'); ?>
<style>
    .stage-progress {
        background: linear-gradient(135deg, #2d2d2d 0%, #1a1a1a 100%);
        border: 1px solid rgba(255, 107, 53, 0.1);
        border-radius: 20px;
        padding: 30px;
        margin-bottom: 30px;
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
    }

    .progress-steps {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 30px;
    }

    .step {
        display: flex;
        flex-direction: column;
        align-items: center;
        text-align: center;
        flex: 1;
        position: relative;
    }

    .step-circle {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.1);
        color: #888;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        margin-bottom: 10px;
        transition: all 0.3s ease;
        border: 2px solid rgba(255, 255, 255, 0.1);
    }

    .step.active .step-circle {
        background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
        color: white;
        border-color: #ff6b35;
        box-shadow: 0 0 20px rgba(255, 107, 53, 0.5);
    }

    .step.completed .step-circle {
        background: linear-gradient(135deg, #00d4aa 0%, #00bf95 100%);
        color: white;
        border-color: #00d4aa;
    }

    .step-title {
        color: #888;
        font-size: 0.9rem;
        font-weight: 600;
    }

    .step.active .step-title {
        color: #ff6b35;
    }

    .step.completed .step-title {
        color: #00d4aa;
    }

    .step-line {
        position: absolute;
        top: 25px;
        left: 50px;
        right: -50px;
        height: 2px;
        background: rgba(255, 255, 255, 0.1);
        z-index: -1;
    }

    .step:last-child .step-line {
        display: none;
    }

    .step.completed .step-line {
        background: linear-gradient(135deg, #00d4aa 0%, #00bf95 100%);
    }

    .gift-form-section {
        background: linear-gradient(145deg, #2d2d2d, #1a1a1a);
        border: 1px solid rgba(255, 107, 53, 0.1);
        border-radius: 20px;
        padding: 40px;
        margin-bottom: 30px;
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
    }

    .form-section-title {
        color: white;
        font-weight: 700;
        font-size: 1.5rem;
        margin-bottom: 30px;
        display: flex;
        align-items: center;
        gap: 15px;
    }

    .form-section-title i {
        color: #ff6b35;
        font-size: 1.8rem;
    }

    .form-control {
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 107, 53, 0.2);
        border-radius: 10px;
        color: white;
        padding: 12px 15px;
        transition: all 0.3s ease;
    }

    .form-control:focus {
        background: rgba(255, 255, 255, 0.15);
        border-color: #ff6b35;
        box-shadow: 0 0 15px rgba(255, 107, 53, 0.3);
        color: white;
    }

    .form-control::placeholder {
        color: #888;
    }

    .form-label {
        color: #b0b0b0;
        font-weight: 600;
        margin-bottom: 8px;
    }

    .form-select {
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 107, 53, 0.2);
        border-radius: 10px;
        color: white;
        padding: 12px 15px;
    }

    .form-select option {
        background: #2d2d2d;
        color: white;
    }

    .image-upload-area {
        border: 2px dashed rgba(255, 107, 53, 0.3);
        border-radius: 15px;
        padding: 40px;
        text-align: center;
        background: rgba(255, 107, 53, 0.05);
        transition: all 0.3s ease;
        cursor: pointer;
    }

    .image-upload-area:hover {
        border-color: #ff6b35;
        background: rgba(255, 107, 53, 0.1);
    }

    .image-upload-area.drag-over {
        border-color: #ff6b35;
        background: rgba(255, 107, 53, 0.15);
    }

    .upload-icon {
        font-size: 3rem;
        color: #ff6b35;
        margin-bottom: 15px;
    }

    .image-preview {
        max-width: 200px;
        max-height: 200px;
        border-radius: 10px;
        margin: 15px auto;
        display: block;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    }

    .btn-primary {
        background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
        border: none;
        border-radius: 10px;
        padding: 12px 30px;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(255, 107, 53, 0.4);
    }

    .btn-secondary {
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 10px;
        color: white;
        padding: 12px 30px;
        font-weight: 600;
    }

    .action-buttons {
        display: flex;
        gap: 15px;
        justify-content: flex-end;
        margin-top: 30px;
    }

    .vendor-selection-card {
        background: linear-gradient(135deg, rgba(255, 107, 53, 0.1) 0%, rgba(255, 107, 53, 0.05) 100%);
        border: 1px solid rgba(255, 107, 53, 0.2);
        border-radius: 15px;
        padding: 20px;
        margin-bottom: 20px;
        transition: all 0.3s ease;
    }

    .vendor-selection-card:hover {
        background: linear-gradient(135deg, rgba(255, 107, 53, 0.2) 0%, rgba(255, 107, 53, 0.1) 100%);
        transform: translateY(-2px);
    }

    .vendor-selection-card.selected {
        border-color: #ff6b35;
        box-shadow: 0 0 20px rgba(255, 107, 53, 0.3);
    }

    .vendor-info {
        display: flex;
        align-items: center;
        gap: 15px;
    }

    .vendor-avatar {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: bold;
        font-size: 1.2rem;
    }

    .vendor-details h6 {
        color: white;
        margin: 0;
        font-weight: 600;
    }

    .vendor-details p {
        color: #888;
        margin: 0;
        font-size: 0.9rem;
    }

    .section-divider {
        height: 1px;
        background: linear-gradient(90deg, transparent, rgba(255, 107, 53, 0.3), transparent);
        margin: 30px 0;
    }
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <!-- Progress Steps -->
    <div class="stage-progress">
        <div class="progress-steps">
            <div class="step active" id="step-1">
                <div class="step-circle">1</div>
                <div class="step-title">اختيار التاجر</div>
                <div class="step-line"></div>
            </div>
            <div class="step" id="step-2">
                <div class="step-circle">2</div>
                <div class="step-title">معلومات الهدية</div>
                <div class="step-line"></div>
            </div>
            <div class="step" id="step-3">
                <div class="step-circle">3</div>
                <div class="step-title">رفع الصورة</div>
                <div class="step-line"></div>
            </div>
            <div class="step" id="step-4">
                <div class="step-circle">4</div>
                <div class="step-title">المراجعة والإرسال</div>
            </div>
        </div>
    </div>

    <form action="<?php echo e(route('admin.gifts.store')); ?>" method="POST" enctype="multipart/form-data" id="gift-form">
        <?php echo csrf_field(); ?>

        <!-- Stage 1: Vendor Selection -->
        <div class="gift-form-section" id="stage-1">
            <h3 class="form-section-title">
                <i class="fas fa-store"></i>
                اختيار التاجر
            </h3>
            
            <div class="row">
                <?php $__currentLoopData = $vendors; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $vendor): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="col-md-6 col-lg-4 mb-3">
                        <div class="vendor-selection-card" data-vendor-id="<?php echo e($vendor->id); ?>">
                            <div class="vendor-info">
                                <div class="vendor-avatar">
                                    <?php echo e(substr($vendor->company_name, 0, 1)); ?>

                                </div>
                                <div class="vendor-details">
                                    <h6><?php echo e($vendor->company_name); ?></h6>
                                    <p><?php echo e($vendor->user->name); ?> • <?php echo e($vendor->user->email); ?></p>
                                    <p><i class="fas fa-phone me-1"></i> <?php echo e($vendor->phone_number); ?></p>
                                </div>
                            </div>
                            <input type="radio" name="vendor_id" value="<?php echo e($vendor->id); ?>" style="display: none;" required>
                        </div>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>

            <?php $__errorArgs = ['vendor_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                <div class="alert alert-danger mt-3"><?php echo e($message); ?></div>
            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
        </div>

        <!-- Stage 2: Gift Information -->
        <div class="gift-form-section" id="stage-2" style="display: none;">
            <h3 class="form-section-title">
                <i class="fas fa-gift"></i>
                معلومات الهدية الأساسية
            </h3>

            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="name" class="form-label">اسم الهدية *</label>
                    <input type="text" class="form-control" id="name" name="name" value="<?php echo e(old('name')); ?>" 
                           placeholder="أدخل اسم الهدية" required>
                    <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <div class="text-danger mt-1"><?php echo e($message); ?></div>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>

                <div class="col-md-6 mb-3">
                    <label for="price" class="form-label">السعر (ريال) *</label>
                    <input type="number" class="form-control" id="price" name="price" value="<?php echo e(old('price')); ?>" 
                           placeholder="0.00" step="0.01" min="0" required>
                    <?php $__errorArgs = ['price'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <div class="text-danger mt-1"><?php echo e($message); ?></div>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>

                <div class="col-md-6 mb-3">
                    <label for="category" class="form-label">فئة الهدية</label>
                    <select class="form-select" id="category" name="category">
                        <option value="">اختر الفئة</option>
                        <option value="هدايا شخصية" <?php echo e(old('category') == 'هدايا شخصية' ? 'selected' : ''); ?>>هدايا شخصية</option>
                        <option value="هدايا أطفال" <?php echo e(old('category') == 'هدايا أطفال' ? 'selected' : ''); ?>>هدايا أطفال</option>
                        <option value="هدايا أعياد" <?php echo e(old('category') == 'هدايا أعياد' ? 'selected' : ''); ?>>هدايا أعياد</option>
                        <option value="زهور" <?php echo e(old('category') == 'زهور' ? 'selected' : ''); ?>>زهور</option>
                        <option value="حلويات" <?php echo e(old('category') == 'حلويات' ? 'selected' : ''); ?>>حلويات</option>
                        <option value="إلكترونيات" <?php echo e(old('category') == 'إلكترونيات' ? 'selected' : ''); ?>>إلكترونيات</option>
                        <option value="أخرى" <?php echo e(old('category') == 'أخرى' ? 'selected' : ''); ?>>أخرى</option>
                    </select>
                    <?php $__errorArgs = ['category'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <div class="text-danger mt-1"><?php echo e($message); ?></div>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>

                <div class="col-md-6 mb-3">
                    <label for="status" class="form-label">حالة الهدية *</label>
                    <select class="form-select" id="status" name="status" required>
                        <option value="قيد الانتظار" <?php echo e(old('status') == 'قيد الانتظار' ? 'selected' : ''); ?>>قيد الانتظار</option>
                        <option value="تم التسليم" <?php echo e(old('status') == 'تم التسليم' ? 'selected' : ''); ?>>تم التسليم</option>
                        <option value="تم الرفض" <?php echo e(old('status') == 'تم الرفض' ? 'selected' : ''); ?>>تم الرفض</option>
                    </select>
                    <?php $__errorArgs = ['status'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <div class="text-danger mt-1"><?php echo e($message); ?></div>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>

                <div class="col-12 mb-3">
                    <label for="description" class="form-label">وصف الهدية *</label>
                    <textarea class="form-control" id="description" name="description" rows="4" 
                              placeholder="اكتب وصفاً مفصلاً للهدية..." required><?php echo e(old('description')); ?></textarea>
                    <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <div class="text-danger mt-1"><?php echo e($message); ?></div>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>

                <div class="col-12 mb-3">
                    <label for="notes" class="form-label">ملاحظات إضافية</label>
                    <textarea class="form-control" id="notes" name="notes" rows="3" 
                              placeholder="أي ملاحظات إضافية حول الهدية..."><?php echo e(old('notes')); ?></textarea>
                    <?php $__errorArgs = ['notes'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <div class="text-danger mt-1"><?php echo e($message); ?></div>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>
            </div>
        </div>

        <!-- Stage 3: Image Upload -->
        <div class="gift-form-section" id="stage-3" style="display: none;">
            <h3 class="form-section-title">
                <i class="fas fa-camera"></i>
                رفع صورة الهدية
            </h3>

            <div class="image-upload-area" id="image-upload-area">
                <div class="upload-icon">
                    <i class="fas fa-cloud-upload-alt"></i>
                </div>
                <h5 style="color: white; margin-bottom: 10px;">اسحب وأفلت الصورة هنا أو انقر للاختيار</h5>
                <p style="color: #888;">الصيغ المدعومة: JPG, PNG, GIF (الحد الأقصى: 2MB)</p>
                <input type="file" id="image" name="image" accept="image/*" style="display: none;" required>
                <img id="image-preview" class="image-preview" style="display: none;">
            </div>

            <?php $__errorArgs = ['image'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                <div class="text-danger mt-3"><?php echo e($message); ?></div>
            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
        </div>

        <!-- Stage 4: Review and Submit -->
        <div class="gift-form-section" id="stage-4" style="display: none;">
            <h3 class="form-section-title">
                <i class="fas fa-check-circle"></i>
                مراجعة البيانات والإرسال
            </h3>

            <div class="row">
                <div class="col-md-6">
                    <h5 style="color: #ff6b35; margin-bottom: 20px;">معلومات التاجر</h5>
                    <div id="selected-vendor-info">
                        <!-- Will be populated by JavaScript -->
                    </div>
                    
                    <div class="section-divider"></div>
                    
                    <h5 style="color: #ff6b35; margin-bottom: 20px;">معلومات الهدية</h5>
                    <div id="gift-info-summary">
                        <!-- Will be populated by JavaScript -->
                    </div>
                </div>
                
                <div class="col-md-6">
                    <h5 style="color: #ff6b35; margin-bottom: 20px;">صورة الهدية</h5>
                    <div id="image-preview-summary">
                        <!-- Will be populated by JavaScript -->
                    </div>
                </div>
            </div>

            <div class="row mt-4">
                <div class="col-12">
                    <div class="form-check">
                        <input type="checkbox" class="form-check-input" id="approved" name="approved" value="1">
                        <label class="form-check-label" for="approved" style="color: #b0b0b0;">
                            اعتماد الهدية فوراً (يمكن تعديل هذا لاحقاً)
                        </label>
                    </div>
                </div>
            </div>
        </div>

        <!-- Navigation Buttons -->
        <div class="action-buttons">
            <button type="button" class="btn btn-secondary" id="prev-btn" style="display: none;">السابق</button>
            <button type="button" class="btn btn-primary" id="next-btn">التالي</button>
            <button type="submit" class="btn btn-primary" id="submit-btn" style="display: none;">
                <i class="fas fa-save me-2"></i>
                إنشاء الهدية
            </button>
            <button type="submit" class="btn btn-secondary" name="save_as_draft" value="1" id="draft-btn" style="display: none;">
                <i class="fas fa-file-alt me-2"></i>
                حفظ كمسودة
            </button>
        </div>
    </form>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    let currentStage = 1;
    const totalStages = 4;

    // Elements
    const stages = document.querySelectorAll('[id^="stage-"]');
    const steps = document.querySelectorAll('[id^="step-"]');
    const nextBtn = document.getElementById('next-btn');
    const prevBtn = document.getElementById('prev-btn');
    const submitBtn = document.getElementById('submit-btn');
    const draftBtn = document.getElementById('draft-btn');

    // Vendor selection
    const vendorCards = document.querySelectorAll('.vendor-selection-card');
    vendorCards.forEach(card => {
        card.addEventListener('click', function() {
            // Remove selection from all cards
            vendorCards.forEach(c => c.classList.remove('selected'));
            // Add selection to clicked card
            this.classList.add('selected');
            // Check the radio button
            this.querySelector('input[type="radio"]').checked = true;
        });
    });

    // Image upload functionality
    const uploadArea = document.getElementById('image-upload-area');
    const imageInput = document.getElementById('image');
    const imagePreview = document.getElementById('image-preview');

    uploadArea.addEventListener('click', () => imageInput.click());
    uploadArea.addEventListener('dragover', (e) => {
        e.preventDefault();
        uploadArea.classList.add('drag-over');
    });
    uploadArea.addEventListener('dragleave', () => {
        uploadArea.classList.remove('drag-over');
    });
    uploadArea.addEventListener('drop', (e) => {
        e.preventDefault();
        uploadArea.classList.remove('drag-over');
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            imageInput.files = files;
            previewImage(files[0]);
        }
    });

    imageInput.addEventListener('change', function() {
        if (this.files && this.files[0]) {
            previewImage(this.files[0]);
        }
    });

    function previewImage(file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            imagePreview.src = e.target.result;
            imagePreview.style.display = 'block';
        };
        reader.readAsDataURL(file);
    }

    // Stage navigation
    function showStage(stageNumber) {
        stages.forEach(stage => stage.style.display = 'none');
        document.getElementById(`stage-${stageNumber}`).style.display = 'block';

        steps.forEach((step, index) => {
            step.classList.remove('active', 'completed');
            if (index + 1 < stageNumber) {
                step.classList.add('completed');
            } else if (index + 1 === stageNumber) {
                step.classList.add('active');
            }
        });

        // Button visibility
        prevBtn.style.display = stageNumber > 1 ? 'inline-block' : 'none';
        nextBtn.style.display = stageNumber < totalStages ? 'inline-block' : 'none';
        submitBtn.style.display = stageNumber === totalStages ? 'inline-block' : 'none';
        draftBtn.style.display = stageNumber === totalStages ? 'inline-block' : 'none';

        // Populate review stage
        if (stageNumber === totalStages) {
            populateReviewStage();
        }
    }

    function populateReviewStage() {
        // Vendor info
        const selectedVendor = document.querySelector('.vendor-selection-card.selected');
        if (selectedVendor) {
            const vendorName = selectedVendor.querySelector('h6').textContent;
            const vendorEmail = selectedVendor.querySelector('p').textContent;
            document.getElementById('selected-vendor-info').innerHTML = `
                <p><strong>اسم الشركة:</strong> ${vendorName}</p>
                <p><strong>التفاصيل:</strong> ${vendorEmail}</p>
            `;
        }

        // Gift info
        const giftInfo = `
            <p><strong>اسم الهدية:</strong> ${document.getElementById('name').value}</p>
            <p><strong>السعر:</strong> ${document.getElementById('price').value} ريال</p>
            <p><strong>الفئة:</strong> ${document.getElementById('category').value || 'غير محدد'}</p>
            <p><strong>الحالة:</strong> ${document.getElementById('status').value}</p>
            <p><strong>الوصف:</strong> ${document.getElementById('description').value}</p>
            ${document.getElementById('notes').value ? `<p><strong>الملاحظات:</strong> ${document.getElementById('notes').value}</p>` : ''}
        `;
        document.getElementById('gift-info-summary').innerHTML = giftInfo;

        // Image preview
        if (imagePreview.src && imagePreview.style.display !== 'none') {
            document.getElementById('image-preview-summary').innerHTML = `
                <img src="${imagePreview.src}" style="max-width: 100%; border-radius: 10px; box-shadow: 0 5px 15px rgba(0,0,0,0.3);">
            `;
        } else {
            document.getElementById('image-preview-summary').innerHTML = '<p style="color: #888;">لم يتم رفع صورة</p>';
        }
    }

    function validateStage(stageNumber) {
        switch (stageNumber) {
            case 1:
                return document.querySelector('input[name="vendor_id"]:checked') !== null;
            case 2:
                const name = document.getElementById('name').value.trim();
                const price = document.getElementById('price').value.trim();
                const description = document.getElementById('description').value.trim();
                return name && price && description;
            case 3:
                return document.getElementById('image').files.length > 0;
            default:
                return true;
        }
    }

    // Navigation events
    nextBtn.addEventListener('click', function() {
        if (validateStage(currentStage)) {
            if (currentStage < totalStages) {
                currentStage++;
                showStage(currentStage);
            }
        } else {
            alert('يرجى إكمال جميع الحقول المطلوبة في هذه المرحلة.');
        }
    });

    prevBtn.addEventListener('click', function() {
        if (currentStage > 1) {
            currentStage--;
            showStage(currentStage);
        }
    });

    // Initialize
    showStage(currentStage);
});
</script>
<?php $__env->stopPush(); ?> 
<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\GiftsSaudi-Laravel (1)\resources\views\admin\gifts\create.blade.php ENDPATH**/ ?>