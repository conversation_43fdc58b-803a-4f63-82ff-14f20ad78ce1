<?php $__env->startSection('title', 'إدارة الموظفين'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-1">
                <i class="fas fa-users me-2"></i>
                إدارة الموظفين
            </h1>
            <p class="text-muted">أضف وادير موظفيك المسؤولين عن تسليم الهدايا</p>
        </div>
        <a href="<?php echo e(route('vendor.employees.create')); ?>" class="btn btn-primary">
            <i class="fas fa-plus me-2"></i>
            إضافة موظف جديد
        </a>
    </div>

    <!-- Stats Row -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-primary text-white">
                <div class="card-body text-center">
                    <h3><?php echo e($employees->count()); ?></h3>
                    <p class="mb-0">إجمالي الموظفين</p>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-success text-white">
                <div class="card-body text-center">
                    <h3><?php echo e($employees->where('status', 'active')->count()); ?></h3>
                    <p class="mb-0">نشط</p>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-warning text-white">
                <div class="card-body text-center">
                    <h3><?php echo e($employees->where('status', 'pending')->count()); ?></h3>
                    <p class="mb-0">في انتظار الموافقة</p>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-danger text-white">
                <div class="card-body text-center">
                    <h3><?php echo e($employees->where('status', 'inactive')->count()); ?></h3>
                    <p class="mb-0">غير نشط</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Employees Table -->
    <div class="card">
        <div class="card-header">
            <h5>قائمة الموظفين</h5>
        </div>
        <div class="card-body">
            <?php if($employees->count() > 0): ?>
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>الصورة</th>
                                <th>اسم الموظف</th>
                                <th>رقم الهاتف</th>
                                <th>البريد الإلكتروني</th>
                                <th>المنصب</th>
                                <th>الحالة</th>
                                <th>التسليمات</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $__currentLoopData = $employees; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $employee): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <tr>
                                    <td><?php echo e($index + 1); ?></td>
                                    <td>
                                        <?php if($employee->avatar): ?>
                                            <img src="<?php echo e(asset('storage/' . $employee->avatar)); ?>" 
                                                 alt="<?php echo e($employee->name); ?>" 
                                                 class="rounded-circle" 
                                                 width="40" 
                                                 height="40">
                                        <?php else: ?>
                                            <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center text-white" 
                                                 style="width: 40px; height: 40px;">
                                                <?php echo e(substr($employee->name, 0, 1)); ?>

                                            </div>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <strong><?php echo e($employee->name); ?></strong>
                                        <?php if($employee->national_id): ?>
                                            <br><small class="text-muted">هوية: <?php echo e($employee->national_id); ?></small>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <a href="tel:<?php echo e($employee->phone); ?>" class="text-decoration-none">
                                            <?php echo e($employee->phone); ?>

                                        </a>
                                    </td>
                                    <td>
                                        <?php if($employee->email): ?>
                                            <a href="mailto:<?php echo e($employee->email); ?>" class="text-decoration-none">
                                                <?php echo e($employee->email); ?>

                                            </a>
                                        <?php else: ?>
                                            <span class="text-muted">غير محدد</span>
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo e($employee->position ?? 'موظف تسليم'); ?></td>
                                    <td>
                                        <?php switch($employee->status):
                                            case ('active'): ?>
                                                <span class="badge bg-success">
                                                    <i class="fas fa-check-circle"></i> نشط
                                                </span>
                                                <?php break; ?>
                                            <?php case ('pending'): ?>
                                                <span class="badge bg-warning">
                                                    <i class="fas fa-clock"></i> في انتظار الموافقة
                                                </span>
                                                <?php break; ?>
                                            <?php default: ?>
                                                <span class="badge bg-danger">
                                                    <i class="fas fa-times-circle"></i> غير نشط
                                                </span>
                                        <?php endswitch; ?>
                                    </td>
                                    <td>
                                        <div class="text-center">
                                            <strong><?php echo e($employee->deliveries->count()); ?></strong>
                                            <br><small class="text-muted">إجمالي</small>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="<?php echo e(route('vendor.employees.show', $employee)); ?>" 
                                               class="btn btn-outline-info">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="<?php echo e(route('vendor.employees.edit', $employee)); ?>" 
                                               class="btn btn-outline-warning">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <?php if($employee->status === 'inactive'): ?>
                                                <button onclick="activateEmployee(<?php echo e($employee->id); ?>)" 
                                                        class="btn btn-outline-success">
                                                    <i class="fas fa-play"></i>
                                                </button>
                                            <?php else: ?>
                                                <button onclick="deactivateEmployee(<?php echo e($employee->id); ?>)" 
                                                        class="btn btn-outline-danger">
                                                    <i class="fas fa-pause"></i>
                                                </button>
                                            <?php endif; ?>
                                            <a href="https://wa.me/<?php echo e(preg_replace('/[^0-9]/', '', $employee->phone)); ?>" 
                                               target="_blank" 
                                               class="btn btn-outline-success">
                                                <i class="fab fa-whatsapp"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </tbody>
                    </table>
                </div>
            <?php else: ?>
                <div class="text-center py-4">
                    <i class="fas fa-users fa-3x text-muted mb-3"></i>
                    <h5>لا يوجد موظفين حتى الآن</h5>
                    <p class="text-muted">ابدأ بإضافة موظفيك لمساعدتك في تسليم الهدايا</p>
                    <a href="<?php echo e(route('vendor.employees.create')); ?>" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>
                        إضافة موظف جديد
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Performance Overview -->
    <?php if($employees->where('status', 'active')->count() > 0): ?>
        <div class="card mt-4">
            <div class="card-header">
                <h5>أداء الموظفين (آخر 30 يوم)</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <?php $__currentLoopData = $employees->where('status', 'active')->take(4); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $employee): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <?php
                            $recentDeliveries = $employee->deliveries()->where('created_at', '>=', now()->subDays(30))->count();
                            $successfulDeliveries = $employee->deliveries()->where('status', 'delivered')->where('created_at', '>=', now()->subDays(30))->count();
                            $successRate = $recentDeliveries > 0 ? ($successfulDeliveries / $recentDeliveries) * 100 : 0;
                        ?>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <div class="card border-0 bg-light">
                                <div class="card-body text-center">
                                    <?php if($employee->avatar): ?>
                                        <img src="<?php echo e(asset('storage/' . $employee->avatar)); ?>" 
                                             alt="<?php echo e($employee->name); ?>" 
                                             class="rounded-circle mb-2" 
                                             width="60" 
                                             height="60">
                                    <?php else: ?>
                                        <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center text-white mx-auto mb-2" 
                                             style="width: 60px; height: 60px; font-size: 1.5rem;">
                                            <?php echo e(substr($employee->name, 0, 1)); ?>

                                        </div>
                                    <?php endif; ?>
                                    <h6 class="mb-1"><?php echo e($employee->name); ?></h6>
                                    <div class="row text-center">
                                        <div class="col">
                                            <small class="text-muted">التسليمات</small>
                                            <div class="fw-bold"><?php echo e($recentDeliveries); ?></div>
                                        </div>
                                        <div class="col">
                                            <small class="text-muted">معدل النجاح</small>
                                            <div class="fw-bold text-success"><?php echo e(number_format($successRate, 1)); ?>%</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            </div>
        </div>
    <?php endif; ?>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
function activateEmployee(employeeId) {
    if (confirm('هل تريد تفعيل هذا الموظف؟')) {
        fetch(`/vendor/employees/${employeeId}/update-status`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({ status: 'active' })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('حدث خطأ في تفعيل الموظف');
            }
        })
        .catch(error => {
            alert('حدث خطأ في الاتصال');
        });
    }
}

function deactivateEmployee(employeeId) {
    if (confirm('هل تريد إلغاء تفعيل هذا الموظف؟')) {
        fetch(`/vendor/employees/${employeeId}/update-status`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({ status: 'inactive' })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('حدث خطأ في إلغاء تفعيل الموظف');
            }
        })
        .catch(error => {
            alert('حدث خطأ في الاتصال');
        });
    }
}
</script>
<?php $__env->stopPush(); ?> 
<?php echo $__env->make('layouts.vendor', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\GiftsSaudi-Laravel (1)\resources\views/vendor/employees/index.blade.php ENDPATH**/ ?>