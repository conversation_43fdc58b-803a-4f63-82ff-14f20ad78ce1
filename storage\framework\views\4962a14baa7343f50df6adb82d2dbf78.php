<!DOCTYPE html>
<html lang="ar" dir="rtl" class="rtl">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    <title>إنشاء حساب جديد - Gifts Saudi | هدايا السعودية</title>

    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700;900&family=Poppins:wght@400;500;600;700;800&display=swap" rel="stylesheet">
    
    <!-- Custom Auth CSS -->
    <link href="<?php echo e(asset('css/auth.css')); ?>" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
</head>

<body class="rtl">
    <div class="login-container" dir="rtl">
        <div class="login-background">
            <div class="shape shape-1"></div>
            <div class="shape shape-2"></div>
            <div class="shape shape-3"></div>
            <div class="shape shape-4"></div>
        </div>

        <!-- Language Switcher -->
        <div class="language-switcher">
            <a href="#" class="lang-btn active" onclick="switchLanguage('ar')">العربية</a>
            <a href="#" class="lang-btn" onclick="switchLanguage('en')">English</a>
        </div>

        <div class="login-card loaded">
            <div class="login-card-inner">
                <div class="login-card-front">
                    <!-- Logo -->
                    <div class="login-logo">
                        <span class="logo-text" data-ar="هدايا السعودية" data-en="Gifts Saudi">هدايا السعودية</span>
                    </div>

                    <!-- Title -->
                    <h1 class="login-title" data-ar="إنشاء حساب جديد" data-en="Create New Account">إنشاء حساب جديد</h1>
                    <p class="login-subtitle" data-ar="انضم إلينا اليوم واستمتع بخدماتنا المتميزة" data-en="Join us today and enjoy our premium services">
                        انضم إلينا اليوم واستمتع بخدماتنا المتميزة
                    </p>

                    <!-- Session Status -->
                    <?php if(session('status')): ?>
                        <div class="success-message">
                            <?php echo e(session('status')); ?>

                        </div>
                    <?php endif; ?>

                    <!-- Validation Errors -->
                    <?php if($errors->any()): ?>
                        <div class="error-message">
                            <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <?php echo e($error); ?><br>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    <?php endif; ?>

                    <!-- Register Form -->
                    <form method="POST" action="<?php echo e(route('register')); ?>" class="login-form">
                        <?php echo csrf_field(); ?>

                        <!-- Role Selection -->
                        <div class="form-group">
                            <label for="role" data-ar="نوع الحساب" data-en="Account Type">نوع الحساب</label>
                            <div class="input-with-icon">
                                <i class="fas fa-user-tag"></i>
                                <select 
                                    id="role" 
                                    name="role" 
                                    required
                                    class="role-select"
                                >
                                    <option value="" data-ar="اختر نوع الحساب" data-en="Select Account Type">اختر نوع الحساب</option>
                                    <option value="admin" data-ar="مدير النظام" data-en="Admin" <?php echo e(old('role') == 'admin' ? 'selected' : ''); ?>>مدير النظام</option>
                                    <option value="vendor" data-ar="متجر / تاجر" data-en="Vendor" <?php echo e(old('role') == 'vendor' ? 'selected' : ''); ?>>متجر / تاجر</option>
                                    <option value="staff" data-ar="موظف" data-en="Staff" <?php echo e(old('role') == 'staff' ? 'selected' : ''); ?>>موظف</option>
                                </select>
                            </div>
                        </div>

                        <!-- Name -->
                        <div class="form-group">
                            <label for="name" data-ar="الاسم الكامل" data-en="Full Name">الاسم الكامل</label>
                            <div class="input-with-icon">
                                <i class="fas fa-user"></i>
                                <input 
                                    id="name" 
                                    type="text" 
                                    name="name" 
                                    value="<?php echo e(old('name')); ?>" 
                                    required 
                                    autofocus 
                                    autocomplete="name"
                                    placeholder="أدخل اسمك الكامل"
                                    data-placeholder-ar="أدخل اسمك الكامل"
                                    data-placeholder-en="Enter your full name"
                                />
                            </div>
                        </div>

                        <!-- Email Address -->
                        <div class="form-group">
                            <label for="email" data-ar="البريد الإلكتروني" data-en="Email">البريد الإلكتروني</label>
                            <div class="input-with-icon">
                                <i class="fas fa-envelope"></i>
                                <input 
                                    id="email" 
                                    type="email" 
                                    name="email" 
                                    value="<?php echo e(old('email')); ?>" 
                                    required 
                                    autocomplete="username"
                                    placeholder="أدخل بريدك الإلكتروني"
                                    data-placeholder-ar="أدخل بريدك الإلكتروني"
                                    data-placeholder-en="Enter your email"
                                />
                            </div>
                        </div>

                        <!-- Phone -->
                        <div class="form-group">
                            <label for="phone" data-ar="رقم الهاتف (اختياري)" data-en="Phone Number (Optional)">رقم الهاتف (اختياري)</label>
                            <div class="input-with-icon">
                                <i class="fas fa-phone"></i>
                                <input 
                                    id="phone" 
                                    type="tel" 
                                    name="phone" 
                                    value="<?php echo e(old('phone')); ?>" 
                                    autocomplete="tel"
                                    placeholder="أدخل رقم هاتفك"
                                    data-placeholder-ar="أدخل رقم هاتفك"
                                    data-placeholder-en="Enter your phone number"
                                />
                            </div>
                        </div>

                        <!-- Password -->
                        <div class="form-group">
                            <label for="password" data-ar="كلمة المرور" data-en="Password">كلمة المرور</label>
                            <div class="input-with-icon">
                                <i class="fas fa-lock"></i>
                                <input 
                                    id="password" 
                                    type="password" 
                                    name="password" 
                                    required 
                                    autocomplete="new-password"
                                    placeholder="أدخل كلمة المرور"
                                    data-placeholder-ar="أدخل كلمة المرور"
                                    data-placeholder-en="Enter your password"
                                />
                            </div>
                            <small class="password-hint" data-ar="يجب أن تحتوي على 8 أحرف على الأقل" data-en="Must be at least 8 characters">
                                يجب أن تحتوي على 8 أحرف على الأقل
                            </small>
                        </div>

                        <!-- Confirm Password -->
                        <div class="form-group">
                            <label for="password_confirmation" data-ar="تأكيد كلمة المرور" data-en="Confirm Password">تأكيد كلمة المرور</label>
                            <div class="input-with-icon">
                                <i class="fas fa-lock"></i>
                                <input 
                                    id="password_confirmation" 
                                    type="password" 
                                    name="password_confirmation" 
                                    required 
                                    autocomplete="new-password"
                                    placeholder="أعد إدخال كلمة المرور"
                                    data-placeholder-ar="أعد إدخال كلمة المرور"
                                    data-placeholder-en="Re-enter your password"
                                />
                            </div>
                        </div>

                        <!-- Terms and Conditions -->
                        <div class="form-group">
                            <div class="remember-me">
                                <input id="terms" type="checkbox" name="terms" required>
                                <label for="terms" data-ar="أوافق على الشروط والأحكام" data-en="I agree to the Terms and Conditions">
                                    أوافق على <a href="#" class="terms-link">الشروط والأحكام</a>
                                </label>
                            </div>
                        </div>

                        <!-- Submit Button -->
                        <button type="submit" class="login-button" data-ar="إنشاء الحساب" data-en="Create Account">
                            إنشاء الحساب
                        </button>

                        <!-- Login Link -->
                        <div class="login-footer">
                            <span data-ar="لديك حساب بالفعل؟" data-en="Already have an account?">لديك حساب بالفعل؟</span>
                            <a href="<?php echo e(route('login')); ?>" class="register-link" data-ar="تسجيل الدخول" data-en="Login">
                                تسجيل الدخول
                            </a>
                        </div>

                        <!-- Back to Home -->
                        <div class="login-footer">
                            <a href="<?php echo e(url('/')); ?>" class="back-to-home" data-ar="العودة للصفحة الرئيسية" data-en="Back to Home">
                                <i class="fas fa-arrow-right rtl-flip"></i>
                                <span>العودة للصفحة الرئيسية</span>
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Language switching functionality
        function switchLanguage(lang) {
            const body = document.body;
            const html = document.documentElement;
            const elements = document.querySelectorAll('[data-ar][data-en]');
            const placeholderElements = document.querySelectorAll('[data-placeholder-ar][data-placeholder-en]');
            const langButtons = document.querySelectorAll('.lang-btn');
            const selectOptions = document.querySelectorAll('option[data-ar][data-en]');

            // Update active language button
            langButtons.forEach(btn => {
                btn.classList.remove('active');
                if (btn.textContent.includes(lang === 'ar' ? 'العربية' : 'English')) {
                    btn.classList.add('active');
                }
            });

            // Update text direction and content
            if (lang === 'ar') {
                body.className = 'rtl';
                html.className = 'rtl';
                html.setAttribute('dir', 'rtl');
                html.setAttribute('lang', 'ar');
            } else {
                body.className = 'ltr';
                html.className = 'ltr';
                html.setAttribute('dir', 'ltr');
                html.setAttribute('lang', 'en');
            }

            // Update all translatable elements
            elements.forEach(element => {
                element.textContent = element.getAttribute('data-' + lang);
            });

            // Update placeholders
            placeholderElements.forEach(element => {
                element.placeholder = element.getAttribute('data-placeholder-' + lang);
            });

            // Update select options
            selectOptions.forEach(option => {
                option.textContent = option.getAttribute('data-' + lang);
            });

            // Store language preference
            localStorage.setItem('preferred-language', lang);
        }

        // Load saved language preference
        document.addEventListener('DOMContentLoaded', function() {
            const savedLang = localStorage.getItem('preferred-language') || 'ar';
            switchLanguage(savedLang);
        });

        // Form submission handling
        document.querySelector('.login-form').addEventListener('submit', function(e) {
            const submitButton = this.querySelector('.login-button');
            const originalText = submitButton.textContent;
            
            submitButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> <span data-ar="جاري الإنشاء..." data-en="Creating...">جاري الإنشاء...</span>';
            submitButton.disabled = true;
            
            // Re-enable if there's an error (form doesn't submit)
            setTimeout(() => {
                if (submitButton.disabled) {
                    submitButton.textContent = originalText;
                    submitButton.disabled = false;
                }
            }, 3000);
        });

        // Role selection change handler
        document.getElementById('role').addEventListener('change', function() {
            const selectedRole = this.value;
            console.log('Selected role:', selectedRole);
            
            // You can add role-specific logic here if needed
            if (selectedRole === 'vendor') {
                // Show vendor-specific fields if needed
            } else if (selectedRole === 'admin') {
                // Show admin-specific fields if needed
            }
        });
    </script>
</body>
</html>
<?php /**PATH D:\GiftsSaudi-Laravel (1)\resources\views/auth/register.blade.php ENDPATH**/ ?>