<div class="message-count-stage">
    <div class="stage-intro mb-4">
        <h5 class="stage-title">حدد عدد الرسائل المراد إرسالها</h5>
        <p class="stage-description">اختر عدد الرسائل التي ترغب في إرسالها من إجمالي العملاء المستهدفين</p>
    </div>

    <div class="message-count-section">
        <!-- Current Stats -->
        <div class="row mb-4">
            <div class="col-md-4">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-value"><?php echo e($campaign->target_client_count ?? 0); ?></div>
                        <div class="stat-label">العملاء المستهدفين</div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-gift"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-value"><?php echo e(number_format($campaign->gift->price ?? 0, 2)); ?></div>
                        <div class="stat-label">سعر الهدية (ريال)</div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-calculator"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-value" id="total-estimated-cost">0.00</div>
                        <div class="stat-label">التكلفة المتوقعة (ريال)</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Message Count Selection -->
        <div class="count-selection-card">
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-sliders-h me-2"></i>
                        اختيار عدد الرسائل
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-8">
                            <div class="form-group">
                                <label for="message_count" class="form-label d-flex justify-content-between">
                                    <span>
                                        <i class="fas fa-envelope me-1"></i>
                                        عدد الرسائل
                                    </span>
                                    <span class="text-muted">الحد الأقصى: <?php echo e($campaign->target_client_count ?? 0); ?></span>
                                </label>
                                
                                <div class="count-input-container">
                                    <input type="range" 
                                           id="message_count_range" 
                                           class="form-range" 
                                           min="1" 
                                           max="<?php echo e($campaign->target_client_count ?? 100); ?>" 
                                           value="<?php echo e(old('message_count', $campaign->message_count ?? 1)); ?>"
                                           step="1">
                                    
                                    <input type="number" 
                                           id="message_count" 
                                           name="message_count" 
                                           class="form-control mt-3 <?php $__errorArgs = ['message_count'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                           min="1" 
                                           max="<?php echo e($campaign->target_client_count ?? 100); ?>" 
                                           value="<?php echo e(old('message_count', $campaign->message_count ?? 1)); ?>" 
                                           required>
                                </div>
                                
                                <?php $__errorArgs = ['message_count'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                
                                <div class="form-text mt-2">
                                    <i class="fas fa-info-circle me-1"></i>
                                    سيتم اختيار العملاء بناءً على الفلاتر المطبقة في المرحلة السابقة
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="percentage-display">
                                <div class="percentage-circle">
                                    <div class="percentage-number" id="percentage-value">0%</div>
                                    <div class="percentage-label">من إجمالي العملاء</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Selection Buttons -->
        <div class="quick-selection mt-4">
            <h6 class="quick-title">
                <i class="fas fa-magic me-2"></i>
                اختيار سريع
            </h6>
            <div class="quick-buttons">
                <button type="button" class="btn btn-outline-primary quick-btn" data-percentage="25">
                    25%
                    <small class="d-block">ربع العملاء</small>
                </button>
                <button type="button" class="btn btn-outline-primary quick-btn" data-percentage="50">
                    50%
                    <small class="d-block">نصف العملاء</small>
                </button>
                <button type="button" class="btn btn-outline-primary quick-btn" data-percentage="75">
                    75%
                    <small class="d-block">ثلاثة أرباع</small>
                </button>
                <button type="button" class="btn btn-outline-primary quick-btn" data-percentage="100">
                    100%
                    <small class="d-block">جميع العملاء</small>
                </button>
            </div>
        </div>

        <!-- Cost Breakdown -->
        <div class="cost-breakdown mt-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-receipt me-2"></i>
                        تفاصيل التكلفة
                    </h6>
                </div>
                <div class="card-body">
                    <div class="breakdown-table">
                        <div class="breakdown-row">
                            <span class="breakdown-label">
                                <i class="fas fa-gift me-2"></i>
                                تكلفة الهدايا (<span id="gifts-count">0</span> هدية)
                            </span>
                            <span class="breakdown-value" id="gifts-cost">0.00 ريال</span>
                        </div>
                        <div class="breakdown-row">
                            <span class="breakdown-label">
                                <i class="fas fa-paper-plane me-2"></i>
                                تكلفة الرسائل (<span id="messages-count">0</span> رسالة)
                            </span>
                            <span class="breakdown-value" id="messages-cost">0.00 ريال</span>
                        </div>
                        <div class="breakdown-row total-row">
                            <span class="breakdown-label">
                                <i class="fas fa-calculator me-2"></i>
                                <strong>الإجمالي</strong>
                            </span>
                            <span class="breakdown-value" id="total-cost">
                                <strong>0.00 ريال</strong>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.message-count-stage {
    padding: 1rem 0;
}

.stage-intro {
    text-align: center;
    margin-bottom: 2rem;
}

.stage-title {
    color: #333;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.stage-description {
    color: #6c757d;
    font-size: 1rem;
}

.stat-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1.5rem;
    border-radius: 15px;
    display: flex;
    align-items: center;
    height: 100%;
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
}

.stat-icon {
    font-size: 2.5rem;
    margin-right: 1rem;
    opacity: 0.8;
}

.stat-content {
    flex: 1;
}

.stat-value {
    font-size: 1.75rem;
    font-weight: 700;
    margin-bottom: 0.25rem;
}

.stat-label {
    font-size: 0.875rem;
    opacity: 0.9;
}

.count-selection-card .card {
    border: none;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    border-radius: 15px;
}

.count-selection-card .card-header {
    background: #f8f9fa;
    border: none;
    border-radius: 15px 15px 0 0;
}

.count-input-container {
    position: relative;
}

.form-range {
    width: 100%;
    margin-bottom: 1rem;
}

.form-range::-webkit-slider-thumb {
    background: #667eea;
}

.form-range::-moz-range-thumb {
    background: #667eea;
    border: none;
}

.percentage-display {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
}

.percentage-circle {
    width: 120px;
    height: 120px;
    border: 4px solid #667eea;
    border-radius: 50%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.05) 100%);
}

.percentage-number {
    font-size: 1.5rem;
    font-weight: 700;
    color: #667eea;
}

.percentage-label {
    font-size: 0.75rem;
    color: #6c757d;
    margin-top: 0.25rem;
}

.quick-selection {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 15px;
    border-left: 4px solid #667eea;
}

.quick-title {
    color: #333;
    font-weight: 600;
    margin-bottom: 1rem;
}

.quick-buttons {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.quick-btn {
    flex: 1;
    min-width: 120px;
    padding: 1rem;
    border-radius: 10px;
    transition: all 0.3s ease;
}

.quick-btn:hover {
    background: #667eea;
    border-color: #667eea;
    color: white;
    transform: translateY(-2px);
}

.quick-btn small {
    font-size: 0.75rem;
    opacity: 0.8;
}

.cost-breakdown .card {
    border: none;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    border-radius: 15px;
}

.cost-breakdown .card-header {
    background: #f8f9fa;
    border: none;
    border-radius: 15px 15px 0 0;
}

.breakdown-table {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.breakdown-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid #f8f9fa;
}

.breakdown-row:last-child {
    border-bottom: none;
}

.breakdown-row.total-row {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.05) 100%);
    padding: 1rem;
    border-radius: 10px;
    margin-top: 0.5rem;
}

.breakdown-label {
    flex: 1;
    color: #495057;
}

.breakdown-value {
    color: #667eea;
    font-weight: 600;
}

@media (max-width: 768px) {
    .quick-buttons {
        flex-direction: column;
    }
    
    .quick-btn {
        min-width: auto;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const maxClients = <?php echo e($campaign->target_client_count ?? 100); ?>;
    const giftPrice = <?php echo e($campaign->gift->price ?? 0); ?>;
    const messageCost = 0.5; // Cost per message
    
    const messageCountRange = document.getElementById('message_count_range');
    const messageCountInput = document.getElementById('message_count');
    const percentageValue = document.getElementById('percentage-value');
    const totalEstimatedCost = document.getElementById('total-estimated-cost');
    
    // Cost breakdown elements
    const giftsCount = document.getElementById('gifts-count');
    const giftsCost = document.getElementById('gifts-cost');
    const messagesCount = document.getElementById('messages-count');
    const messagesCost = document.getElementById('messages-cost');
    const totalCost = document.getElementById('total-cost');
    
    const quickButtons = document.querySelectorAll('.quick-btn');

    function updateCalculations() {
        const messageCount = parseInt(messageCountInput.value) || 0;
        
        // Update range slider
        messageCountRange.value = messageCount;
        
        // Calculate percentage
        const percentage = maxClients > 0 ? Math.round((messageCount / maxClients) * 100) : 0;
        percentageValue.textContent = percentage + '%';
        
        // Calculate costs
        const totalGiftsCost = messageCount * giftPrice;
        const totalMessagesCost = messageCount * messageCost;
        const totalCampaignCost = totalGiftsCost + totalMessagesCost;
        
        // Update cost breakdown
        giftsCount.textContent = messageCount;
        giftsCost.textContent = totalGiftsCost.toLocaleString() + ' ريال';
        messagesCount.textContent = messageCount;
        messagesCost.textContent = totalMessagesCost.toLocaleString() + ' ريال';
        totalCost.innerHTML = '<strong>' + totalCampaignCost.toLocaleString() + ' ريال</strong>';
        
        // Update main total
        totalEstimatedCost.textContent = totalCampaignCost.toLocaleString();
    }

    function setMessageCount(count) {
        messageCountInput.value = count;
        updateCalculations();
    }

    // Event listeners
    messageCountRange.addEventListener('input', function() {
        messageCountInput.value = this.value;
        updateCalculations();
    });

    messageCountInput.addEventListener('input', function() {
        const value = parseInt(this.value) || 0;
        if (value > maxClients) {
            this.value = maxClients;
        } else if (value < 1) {
            this.value = 1;
        }
        updateCalculations();
    });

    // Quick selection buttons
    quickButtons.forEach(button => {
        button.addEventListener('click', function() {
            const percentage = parseInt(this.dataset.percentage);
            const count = Math.round((percentage / 100) * maxClients);
            setMessageCount(count);
            
            // Visual feedback
            quickButtons.forEach(btn => btn.classList.remove('active'));
            this.classList.add('active');
        });
    });

    // Initial calculation
    updateCalculations();

    // Validation
    messageCountInput.addEventListener('blur', function() {
        const value = parseInt(this.value) || 0;
        if (value < 1) {
            this.value = 1;
            updateCalculations();
        } else if (value > maxClients) {
            this.value = maxClients;
            updateCalculations();
        }
    });
});
</script>
<?php /**PATH D:\GiftsSaudi-Laravel (1)\resources\views/vendor/campaigns/stages/message-count.blade.php ENDPATH**/ ?>