

<?php $__env->startSection('page-title', 'الهدايا المعلقة'); ?>

<?php $__env->startSection('content'); ?>
<!-- Client Information -->
<div class="client-section mb-4">
    <div class="client-card">
        <div class="client-header">
            <div class="client-avatar">
                <i class="fas fa-user text-success"></i>
            </div>
            <div class="client-info">
                <h5 class="client-name"><?php echo e($client->name); ?></h5>
                <div class="client-details">
                    <div class="detail-item">
                        <i class="fas fa-phone me-2 text-muted"></i>
                        <span><?php echo e($client->phone_number); ?></span>
                    </div>
                    <?php if($client->city): ?>
                    <div class="detail-item">
                        <i class="fas fa-map-marker-alt me-2 text-muted"></i>
                        <span><?php echo e($client->city); ?></span>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Deliveries Count -->
<div class="count-section mb-4">
    <div class="count-card">
        <div class="count-content">
            <div class="count-number"><?php echo e($deliveries->count()); ?></div>
            <div class="count-label">
                <?php echo e($deliveries->count() === 1 ? 'هدية معلقة' : 'هدايا معلقة'); ?>

            </div>
        </div>
        <div class="count-icon">
            <i class="fas fa-gift text-warning"></i>
        </div>
    </div>
</div>

<!-- Deliveries List -->
<div class="deliveries-section">
    <h6 class="deliveries-title mb-3">
        <i class="fas fa-box me-2 text-info"></i>
        الهدايا المعلقة للتسليم
    </h6>
    
    <?php $__currentLoopData = $deliveries; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $delivery): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
    <div class="delivery-card" id="delivery-<?php echo e($delivery->id); ?>">
        <div class="delivery-content">
            <!-- Gift Information -->
            <div class="gift-section">
                <div class="gift-header">
                    <div class="gift-icon">
                        <i class="fas fa-gift text-success"></i>
                    </div>
                    <div class="gift-info">
                        <h6 class="gift-name"><?php echo e($delivery->gift->name); ?></h6>
                        <?php if($delivery->gift->description): ?>
                        <p class="gift-description"><?php echo e($delivery->gift->description); ?></p>
                        <?php endif; ?>
                    </div>
                </div>
                
                <?php if($delivery->campaign): ?>
                <div class="campaign-info">
                    <small class="text-muted">
                        <i class="fas fa-bullhorn me-1"></i>
                        من حملة: <?php echo e($delivery->campaign->campaign_name); ?>

                    </small>
                </div>
                <?php endif; ?>
            </div>
            
            <!-- Delivery Information -->
            <div class="delivery-info">
                <div class="info-grid">
                    <div class="info-item">
                        <i class="fas fa-calendar text-muted"></i>
                        <span><?php echo e($delivery->created_at->format('Y/m/d')); ?></span>
                    </div>
                    <div class="info-item">
                        <i class="fas fa-clock text-muted"></i>
                        <span><?php echo e($delivery->created_at->diffForHumans()); ?></span>
                    </div>
                </div>
            </div>
            
            <!-- Action Buttons -->
            <div class="action-section">
                <button class="btn btn-confirm-mobile" 
                        onclick="confirmDelivery(<?php echo e($delivery->id); ?>, '<?php echo e($delivery->gift->name); ?>')"
                        id="confirm-btn-<?php echo e($delivery->id); ?>">
                    <span class="btn-text">
                        <i class="fas fa-check me-2"></i>
                        تأكيد التسليم
                    </span>
                    <div class="btn-loading" style="display: none;">
                        <i class="fas fa-spinner fa-spin me-2"></i>
                        جاري التأكيد...
                    </div>
                </button>
                
                <button class="btn btn-details-mobile" 
                        onclick="showDeliveryDetails(<?php echo e($delivery->id); ?>)">
                    <i class="fas fa-info-circle me-2"></i>
                    تفاصيل
                </button>
            </div>
        </div>
    </div>
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
</div>

<!-- Success Animation (Hidden by default) -->
<div class="success-overlay" id="successOverlay" style="display: none;">
    <div class="success-content">
        <div class="success-animation">
            <div class="success-checkmark">
                <i class="fas fa-check"></i>
            </div>
            <h4 class="success-title">تم بنجاح!</h4>
            <p class="success-message" id="successMessage"></p>
            <button class="btn btn-success-mobile" onclick="hideSuccessOverlay()">
                <i class="fas fa-thumbs-up me-2"></i>
                ممتاز
            </button>
        </div>
    </div>
</div>

<!-- Back to Search -->
<div class="back-section mt-4">
    <a href="<?php echo e(route('employee.search')); ?>" class="btn btn-outline-success w-100">
        <i class="fas fa-arrow-right me-2"></i>
        بحث عن عميل آخر
    </a>
</div>

<!-- Delivery Details Modal -->
<div class="modal fade" id="deliveryDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تفاصيل التسليم</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="deliveryDetailsContent">
                <div class="text-center">
                    <div class="spinner-border text-success" role="status">
                        <span class="visually-hidden">جاري التحميل...</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<style>
.client-card {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    border-radius: 15px;
    padding: 1.5rem;
    color: white;
    box-shadow: 0 5px 15px rgba(40, 167, 69, 0.3);
}

.client-header {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.client-avatar {
    width: 60px;
    height: 60px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    flex-shrink: 0;
}

.client-name {
    margin: 0 0 0.5rem 0;
    font-weight: 600;
}

.client-details {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.detail-item {
    display: flex;
    align-items: center;
    font-size: 0.9rem;
    opacity: 0.9;
}

.count-card {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-left: 4px solid #ffc107;
}

.count-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: #ffc107;
    line-height: 1;
}

.count-label {
    color: #6c757d;
    font-weight: 500;
    margin-top: 0.25rem;
}

.count-icon {
    font-size: 2rem;
    color: #ffc107;
    opacity: 0.3;
}

.deliveries-title {
    color: #343a40;
    font-weight: 600;
}

.delivery-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    border: 1px solid #e9ecef;
    margin-bottom: 1rem;
    overflow: hidden;
    transition: all 0.3s ease;
}

.delivery-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.delivery-content {
    padding: 1.5rem;
}

.gift-header {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    margin-bottom: 1rem;
}

.gift-icon {
    width: 50px;
    height: 50px;
    background: rgba(40, 167, 69, 0.1);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    flex-shrink: 0;
}

.gift-name {
    margin: 0 0 0.5rem 0;
    color: #343a40;
    font-weight: 600;
}

.gift-description {
    margin: 0;
    color: #6c757d;
    font-size: 0.9rem;
    line-height: 1.4;
}

.campaign-info {
    margin-top: 0.5rem;
    padding-top: 0.5rem;
    border-top: 1px solid #f8f9fa;
}

.delivery-info {
    margin: 1rem 0;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 10px;
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 0.75rem;
}

.info-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.85rem;
    color: #6c757d;
}

.action-section {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 0.75rem;
    margin-top: 1rem;
}

.btn-confirm-mobile {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    border: none;
    color: white;
    padding: 1rem;
    border-radius: 10px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-confirm-mobile:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(40, 167, 69, 0.4);
}

.btn-details-mobile {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    color: #6c757d;
    padding: 1rem;
    border-radius: 10px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-details-mobile:hover {
    background: #e9ecef;
    color: #495057;
}

.btn-outline-success {
    border: 2px solid #28a745;
    color: #28a745;
    background: transparent;
    padding: 1rem;
    border-radius: 10px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-outline-success:hover {
    background: #28a745;
    color: white;
    transform: translateY(-2px);
}

/* Success Overlay */
.success-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.success-content {
    background: white;
    border-radius: 20px;
    padding: 2rem;
    text-align: center;
    max-width: 300px;
    width: 90%;
}

.success-checkmark {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-size: 2.5rem;
    margin-bottom: 1rem;
    animation: successPop 0.6s ease-out;
}

.success-title {
    color: #343a40;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.success-message {
    color: #6c757d;
    margin-bottom: 1.5rem;
}

@keyframes successPop {
    0% { transform: scale(0); }
    80% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

/* Mobile optimizations */
@media (max-width: 576px) {
    .client-card, .delivery-content {
        padding: 1rem;
    }
    
    .client-avatar {
        width: 50px;
        height: 50px;
        font-size: 1.25rem;
    }
    
    .gift-icon {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }
    
    .info-grid {
        grid-template-columns: 1fr;
        gap: 0.5rem;
    }
    
    .action-section {
        grid-template-columns: 1fr;
    }
    
    .count-number {
        font-size: 2rem;
    }
}

/* Loading state */
.delivery-card.loading {
    opacity: 0.6;
    pointer-events: none;
}

.delivery-card.delivered {
    opacity: 0.3;
    transform: scale(0.95);
}
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
// Confirm delivery function
async function confirmDelivery(deliveryId, giftName) {
    const confirmBtn = document.getElementById(`confirm-btn-${deliveryId}`);
    const btnText = confirmBtn.querySelector('.btn-text');
    const btnLoading = confirmBtn.querySelector('.btn-loading');
    const deliveryCard = document.getElementById(`delivery-${deliveryId}`);
    
    if (confirm(`هل أنت متأكد من تأكيد تسليم "${giftName}" للعميل؟`)) {
        // Show loading state
        confirmBtn.disabled = true;
        btnText.style.display = 'none';
        btnLoading.style.display = 'inline-block';
        deliveryCard.classList.add('loading');
        
        try {
            const response = await fetch(`/employee/deliveries/${deliveryId}/confirm`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': csrfToken,
                    'Accept': 'application/json'
                }
            });
            
            const data = await response.json();
            
            if (data.success) {
                // Show success animation
                showSuccessOverlay(`تم تأكيد تسليم "${giftName}" بنجاح!`);
                
                // Mark delivery as completed
                deliveryCard.classList.add('delivered');
                confirmBtn.innerHTML = '<i class="fas fa-check me-2"></i>تم التسليم';
                confirmBtn.classList.remove('btn-confirm-mobile');
                confirmBtn.classList.add('btn-success');
                
                // Remove from pending count
                updatePendingCount();
                
            } else {
                throw new Error(data.message || 'حدث خطأ غير متوقع');
            }
            
        } catch (error) {
            alert('حدث خطأ: ' + error.message);
            
            // Reset button state
            confirmBtn.disabled = false;
            btnText.style.display = 'inline-block';
            btnLoading.style.display = 'none';
            deliveryCard.classList.remove('loading');
        }
    }
}

// Show success overlay
function showSuccessOverlay(message) {
    const overlay = document.getElementById('successOverlay');
    const messageEl = document.getElementById('successMessage');
    
    messageEl.textContent = message;
    overlay.style.display = 'flex';
    
    // Auto hide after 3 seconds
    setTimeout(hideSuccessOverlay, 3000);
}

// Hide success overlay
function hideSuccessOverlay() {
    const overlay = document.getElementById('successOverlay');
    overlay.style.display = 'none';
}

// Update pending count
function updatePendingCount() {
    const countNumber = document.querySelector('.count-number');
    const countLabel = document.querySelector('.count-label');
    const pendingCards = document.querySelectorAll('.delivery-card:not(.delivered)').length;
    
    countNumber.textContent = pendingCards;
    countLabel.textContent = pendingCards === 1 ? 'هدية معلقة' : 'هدايا معلقة';
    
    if (pendingCards === 0) {
        setTimeout(() => {
            const deliveriesSection = document.querySelector('.deliveries-section');
            deliveriesSection.innerHTML = `
                <div class="text-center py-4">
                    <div class="success-checkmark mb-3" style="display: inline-flex;">
                        <i class="fas fa-check"></i>
                    </div>
                    <h5 class="text-success">تم تسليم جميع الهدايا!</h5>
                    <p class="text-muted">تم تأكيد تسليم جميع الهدايا بنجاح</p>
                    <a href="<?php echo e(route('employee.search')); ?>" class="btn btn-success-mobile mt-2">
                        <i class="fas fa-search me-2"></i>
                        بحث عن عميل آخر
                    </a>
                </div>
            `;
        }, 1000);
    }
}

// Show delivery details
async function showDeliveryDetails(deliveryId) {
    const modal = new bootstrap.Modal(document.getElementById('deliveryDetailsModal'));
    const content = document.getElementById('deliveryDetailsContent');
    
    modal.show();
    
    try {
        const response = await fetch(`/employee/deliveries/${deliveryId}/details`);
        const data = await response.json();
        
        if (data.success) {
            content.innerHTML = `
                <div class="delivery-details">
                    <div class="detail-row">
                        <strong>اسم العميل:</strong>
                        <span>${data.delivery.client_name}</span>
                    </div>
                    <div class="detail-row">
                        <strong>رقم الجوال:</strong>
                        <span>${data.delivery.client_phone}</span>
                    </div>
                    <div class="detail-row">
                        <strong>اسم الهدية:</strong>
                        <span>${data.delivery.gift_name}</span>
                    </div>
                    <div class="detail-row">
                        <strong>وصف الهدية:</strong>
                        <span>${data.delivery.gift_description || 'غير متوفر'}</span>
                    </div>
                    <div class="detail-row">
                        <strong>الحملة:</strong>
                        <span>${data.delivery.campaign_name}</span>
                    </div>
                    <div class="detail-row">
                        <strong>تاريخ الإنشاء:</strong>
                        <span>${data.delivery.created_at}</span>
                    </div>
                </div>
            `;
        } else {
            throw new Error(data.message);
        }
        
    } catch (error) {
        content.innerHTML = `
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-circle me-2"></i>
                حدث خطأ في تحميل التفاصيل: ${error.message}
            </div>
        `;
    }
}

// Add CSS for detail rows
const detailRowStyle = document.createElement('style');
detailRowStyle.textContent = `
    .detail-row {
        display: flex;
        justify-content: space-between;
        padding: 0.75rem 0;
        border-bottom: 1px solid #f8f9fa;
    }
    .detail-row:last-child {
        border-bottom: none;
    }
    .detail-row strong {
        color: #495057;
        flex-shrink: 0;
        margin-left: 1rem;
    }
    .detail-row span {
        color: #6c757d;
        text-align: left;
    }
`;
document.head.appendChild(detailRowStyle);
</script>
<?php $__env->stopPush(); ?> 
<?php echo $__env->make('layouts.employee', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\GiftsSaudi-Laravel (1)\resources\views\employee\deliveries.blade.php ENDPATH**/ ?>