

<?php $__env->startSection('title', 'إدارة العملاء'); ?>
<?php $__env->startSection('page-title', 'إدارة العملاء'); ?>

<?php $__env->startPush('styles'); ?>
<style>
    /* Professional Clients Management Styles */
    .clients-container {
        background: transparent;
    }

    /* Page Header */
    .page-header {
        background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
        border: 1px solid rgba(255, 107, 53, 0.2);
        border-radius: 25px;
        padding: 30px;
        margin-bottom: 30px;
        position: relative;
        overflow: hidden;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4);
    }
    

    .page-header::before {
        content: '';
        position: absolute;
        top: -50%;
        right: -50%;
        width: 200%;
        height: 200%;
        background: linear-gradient(45deg, transparent, rgba(255, 107, 53, 0.1), transparent);
        transform: rotate(45deg);
        animation: headerShimmer 4s ease-in-out infinite;
    }

    @keyframes headerShimmer {
        0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
        100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
    }

    .page-title {
        color: white;
        font-weight: 800;
        font-size: 2.2rem;
        margin-bottom: 10px;
        background: linear-gradient(135deg, #ffffff 0%, #ff6b35 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        position: relative;
        z-index: 2;
    }

    .page-subtitle {
        color: #b0b0b0;
        font-size: 1rem;
        position: relative;
        z-index: 2;
    }

    /* Statistics Cards */
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 25px;
        margin-bottom: 40px;
    }

    .stat-card {
        background: linear-gradient(145deg, #2d2d2d, #1a1a1a);
        border: 1px solid rgba(255, 107, 53, 0.1);
        border-radius: 20px;
        padding: 30px;
        position: relative;
        overflow: hidden;
        transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
    }

    .stat-card:hover {
        transform: translateY(-8px) scale(1.02);
        box-shadow: 0 25px 50px rgba(0, 0, 0, 0.4);
        border-color: rgba(255, 107, 53, 0.3);
    }

    .stat-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: var(--card-gradient);
        border-radius: 20px 20px 0 0;
    }

    .stat-card.primary::before { background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%); }
    .stat-card.success::before { background: linear-gradient(135deg, #00d4aa 0%, #00bf95 100%); }
    .stat-card.info::before { background: linear-gradient(135deg, #3867d6 0%, #8e44ad 100%); }
    .stat-card.warning::before { background: linear-gradient(135deg, #ffc107 0%, #ff8f00 100%); }

    .stat-content {
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .stat-info h4 {
        font-size: 2.2rem;
        font-weight: 800;
        color: white;
        margin-bottom: 5px;
    }

    .stat-info p {
        color: #b0b0b0;
        font-size: 0.9rem;
        font-weight: 600;
        margin: 0;
    }

    .stat-icon {
        width: 60px;
        height: 60px;
        border-radius: 15px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.8rem;
        color: white;
        background: var(--card-gradient);
        box-shadow: 0 10px 25px rgba(255, 107, 53, 0.3);
    }

    .stat-card.primary .stat-icon { 
        background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
        box-shadow: 0 10px 25px rgba(255, 107, 53, 0.3);
    }
    .stat-card.success .stat-icon { 
        background: linear-gradient(135deg, #00d4aa 0%, #00bf95 100%);
        box-shadow: 0 10px 25px rgba(0, 212, 170, 0.3);
    }
    .stat-card.info .stat-icon { 
        background: linear-gradient(135deg, #3867d6 0%, #8e44ad 100%);
        box-shadow: 0 10px 25px rgba(56, 103, 214, 0.3);
    }
    .stat-card.warning .stat-icon { 
        background: linear-gradient(135deg, #ffc107 0%, #ff8f00 100%);
        box-shadow: 0 10px 25px rgba(255, 193, 7, 0.3);
    }

    /* Filters Card */
    .filter-card {
        background: linear-gradient(145deg, #2d2d2d, #1a1a1a);
        border: 1px solid rgba(255, 107, 53, 0.1);
        border-radius: 20px;
        padding: 25px;
        margin-bottom: 30px;
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
    }

    .filter-card .form-label {
        color: #e0e0e0;
        font-weight: 600;
        margin-bottom: 8px;
    }

    .filter-card .form-control,
    .filter-card .form-select {
        background: rgba(255, 255, 255, 0.05);
        border: 1px solid rgba(255, 107, 53, 0.2);
        border-radius: 12px;
        color: white;
        padding: 12px 15px;
    }

    .filter-card .form-control:focus,
    .filter-card .form-select:focus {
        background: rgba(255, 255, 255, 0.08);
        border-color: var(--accent-color);
        box-shadow: 0 0 0 0.2rem rgba(255, 107, 53, 0.25);
        color: white;
    }

    .filter-card .form-control::placeholder {
        color: #888;
    }

    .filter-card .btn-primary {
        background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
        border: none;
        border-radius: 12px;
        padding: 12px 20px;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .filter-card .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(255, 107, 53, 0.4);
    }

    .filter-card .btn-outline-secondary {
        border: 1px solid rgba(255, 255, 255, 0.2);
        color: #b0b0b0;
        border-radius: 12px;
        padding: 12px 20px;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .filter-card .btn-outline-secondary:hover {
        background: rgba(255, 255, 255, 0.1);
        border-color: rgba(255, 255, 255, 0.3);
        color: white;
    }

    /* Clients Table */
    .clients-table-card {
        background: linear-gradient(145deg, #2d2d2d, #1a1a1a);
        border: 1px solid rgba(255, 107, 53, 0.1);
        border-radius: 20px;
        overflow: hidden;
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
    }

    .clients-table-card .table {
        margin: 0;
        color: #e0e0e0;
    }

    .clients-table-card .table thead th {
        background: linear-gradient(135deg, #1a1a1a 0%, #0f0f0f 100%);
        border: none;
        color: white !important;
        font-weight: 700;
        padding: 20px 15px;
        font-size: 0.9rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .clients-table-card .table tbody tr {
        border-bottom: 1px solid rgba(255, 107, 53, 0.1);
        transition: all 0.3s ease;
    }

    .clients-table-card .table tbody tr:hover {
        background: rgba(255, 107, 53, 0.05);
    }

    .clients-table-card .table tbody td {
        border: none;
        padding: 20px 15px;
        vertical-align: middle;
    }

    /* Client Avatar */
    .client-avatar {
        width: 45px;
        height: 45px;
        border-radius: 50%;
        background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.2rem;
        font-weight: bold;
        margin-left: 10px;
    }

    /* Status Badges */
    .status-badge {
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 0.75rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .status-active { 
        background: linear-gradient(135deg, rgba(0, 212, 170, 0.2), rgba(0, 191, 149, 0.2)); 
        color: #00d4aa; 
        border: 1px solid rgba(0, 212, 170, 0.3);
    }
    .status-inactive { 
        background: linear-gradient(135deg, rgba(255, 71, 87, 0.2), rgba(196, 69, 105, 0.2)); 
        color: #ff4757; 
        border: 1px solid rgba(255, 71, 87, 0.3);
    }
    .status-pending { 
        background: linear-gradient(135deg, rgba(255, 193, 7, 0.2), rgba(255, 143, 0, 0.2)); 
        color: #ffc107; 
        border: 1px solid rgba(255, 193, 7, 0.3);
    }

    .payment-paid { 
        background: linear-gradient(135deg, rgba(0, 212, 170, 0.2), rgba(0, 191, 149, 0.2)); 
        color: #00d4aa; 
        border: 1px solid rgba(0, 212, 170, 0.3);
    }
    .payment-unpaid { 
        background: linear-gradient(135deg, rgba(255, 71, 87, 0.2), rgba(196, 69, 105, 0.2)); 
        color: #ff4757; 
        border: 1px solid rgba(255, 71, 87, 0.3);
    }
    .payment-partial { 
        background: linear-gradient(135deg, rgba(255, 193, 7, 0.2), rgba(255, 143, 0, 0.2)); 
        color: #ffc107; 
        border: 1px solid rgba(255, 193, 7, 0.3);
    }

    /* Action Buttons */
    .action-buttons .btn {
        margin: 0 2px;
        padding: 8px 12px;
        font-size: 0.8rem;
        border-radius: 8px;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .action-buttons .btn-outline-primary {
        border-color: rgba(255, 107, 53, 0.5);
        color: #ff6b35;
    }

    .action-buttons .btn-outline-primary:hover {
        background: #ff6b35;
        border-color: #ff6b35;
        color: white;
        transform: translateY(-2px);
    }

    .action-buttons .btn-outline-success {
        border-color: rgba(0, 212, 170, 0.5);
        color: #00d4aa;
    }

    .action-buttons .btn-outline-success:hover {
        background: #00d4aa;
        border-color: #00d4aa;
        color: white;
        transform: translateY(-2px);
    }

    .action-buttons .btn-outline-danger {
        border-color: rgba(255, 71, 87, 0.5);
        color: #ff4757;
    }

    .action-buttons .btn-outline-danger:hover {
        background: #ff4757;
        border-color: #ff4757;
        color: white;
        transform: translateY(-2px);
    }

    /* Pagination */
    .pagination {
        justify-content: center;
        margin-top: 30px;
        gap: 5px;
    }

    .pagination .page-item {
        margin: 0 2px;
    }

    .pagination .page-link {
        background: rgba(255, 255, 255, 0.05);
        border: 1px solid rgba(255, 107, 53, 0.2);
        color: #b0b0b0;
        border-radius: 10px;
        padding: 12px 16px;
        transition: all 0.3s ease;
        font-weight: 500;
        min-width: 45px;
        text-align: center;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .pagination .page-link:hover {
        background: rgba(255, 107, 53, 0.15);
        border-color: var(--accent-color);
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(255, 107, 53, 0.3);
    }

    .pagination .page-item.active .page-link {
        background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
        border-color: var(--accent-color);
        color: white;
        transform: translateY(-1px);
        box-shadow: 0 5px 20px rgba(255, 107, 53, 0.4);
    }

    .pagination .page-item.disabled .page-link {
        background: rgba(255, 255, 255, 0.02);
        border-color: rgba(255, 255, 255, 0.1);
        color: #555;
        cursor: not-allowed;
    }

    .pagination .page-link[rel="first"],
    .pagination .page-link[rel="prev"],
    .pagination .page-link[rel="next"],
    .pagination .page-link[rel="last"] {
        background: rgba(255, 107, 53, 0.1);
        border-color: rgba(255, 107, 53, 0.3);
        color: #ff6b35;
        font-weight: 600;
        padding: 12px 18px;
    }

    .pagination .page-link[rel="first"]:hover,
    .pagination .page-link[rel="prev"]:hover,
    .pagination .page-link[rel="next"]:hover,
    .pagination .page-link[rel="last"]:hover {
        background: rgba(255, 107, 53, 0.2);
        color: white;
    }

    /* Client Details */
    .client-details {
        font-size: 0.85rem;
        color: #b0b0b0;
        line-height: 1.4;
    }

    .client-name {
        color: white;
        font-weight: 600;
        font-size: 0.95rem;
        margin-bottom: 3px;
    }

    .client-contact {
        color: #888;
        font-size: 0.8rem;
    }

    /* Add Client Button */
    .add-client-btn {
        background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
        border: none;
        border-radius: 15px;
        padding: 12px 25px;
        font-weight: 700;
        color: white;
        text-decoration: none;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        gap: 8px;
    }

    .add-client-btn:hover {
        color: white;
        transform: translateY(-3px);
        box-shadow: 0 10px 30px rgba(255, 107, 53, 0.4);
    }

    /* Text colors for consistency */
    .text-secondary,
    .text-body-secondary,
    small,
    .form-text {
        color: #b0b0b0 !important;
    }

    /* Excel controls styling */
    .excel-controls {
        display: flex;
        gap: 10px;
        align-items: center;
    }

    .excel-controls .btn-outline-success {
        border-color: rgba(40, 167, 69, 0.5);
        color: #28a745;
        background: rgba(40, 167, 69, 0.1);
        transition: all 0.3s ease;
    }

    .excel-controls .btn-outline-success:hover {
        background: rgba(40, 167, 69, 0.2);
        border-color: #28a745;
        color: white;
        transform: translateY(-2px);
    }

    .excel-controls .btn-success {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        border: none;
        border-radius: 10px;
        padding: 10px 20px;
        font-weight: 600;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
    }

    .excel-controls .btn-success:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(40, 167, 69, 0.4);
    }

    .dropdown-menu {
        background: rgba(33, 37, 41, 0.95);
        border: 1px solid rgba(255, 255, 255, 0.15);
        backdrop-filter: blur(10px);
        border-radius: 10px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    }

    .dropdown-item {
        color: #b0b0b0;
        transition: all 0.3s ease;
        border-radius: 8px;
        margin: 2px 5px;
    }

    .dropdown-item:hover {
        background: rgba(255, 107, 53, 0.1);
        color: white;
    }

    .dropdown-divider {
        border-top: 1px solid rgba(255, 255, 255, 0.15);
    }

    /* Status badge improvements */
    .status-badge.dropdown-toggle::after {
        margin-left: 5px;
    }

    .status-badge:hover {
        transform: none;
        box-shadow: 0 0 10px rgba(255, 107, 53, 0.3);
    }

    /* Bulk actions */
    .bulk-actions {
        background: rgba(255, 255, 255, 0.03);
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 15px;
        padding: 15px;
        margin-bottom: 20px;
        display: none;
    }

    .bulk-actions.show {
        display: block;
    }

    /* Import feedback styling */
    .import-feedback {
        background: rgba(255, 255, 255, 0.05);
        border: 1px solid rgba(255, 107, 53, 0.2);
        border-radius: 10px;
        padding: 15px;
        margin-bottom: 20px;
    }

    .import-feedback .details {
        font-size: 0.9rem;
        color: #b0b0b0;
        margin-top: 10px;
    }

    /* Dynamic fields styling */
    .dynamic-field {
        background: rgba(255, 107, 53, 0.1);
        color: #ff6b35 !important;
        font-weight: 600;
        border-left: 2px solid rgba(255, 107, 53, 0.3);
        min-width: 120px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .dynamic-field-value {
        background: rgba(255, 107, 53, 0.05);
        border-left: 1px solid rgba(255, 107, 53, 0.2);
        min-width: 120px;
        max-width: 150px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        padding: 8px 12px;
    }

    .dynamic-field-value:hover {
        background: rgba(255, 107, 53, 0.1);
        white-space: normal;
        word-break: break-word;
    }

    /* Table scrolling for many columns */
    .table-responsive {
        max-width: 100%;
        overflow-x: auto;
        scrollbar-width: thin;
        scrollbar-color: rgba(255, 107, 53, 0.3) rgba(0, 0, 0, 0.1);
    }

    .table-responsive::-webkit-scrollbar {
        height: 8px;
    }

    .table-responsive::-webkit-scrollbar-track {
        background: rgba(0, 0, 0, 0.1);
        border-radius: 4px;
    }

    .table-responsive::-webkit-scrollbar-thumb {
        background: rgba(255, 107, 53, 0.3);
        border-radius: 4px;
    }

    .table-responsive::-webkit-scrollbar-thumb:hover {
        background: rgba(255, 107, 53, 0.5);
    }
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
<div class="clients-container">
    <!-- Page Header -->
    <div class="page-header">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="page-title">إدارة العملاء</h1>
                <p class="page-subtitle">تتبع وإدارة بيانات العملاء ومعاملاتهم</p>
            </div>
            <div class="d-flex gap-3">
                <!-- Add Client Button -->
                <a href="<?php echo e(route('admin.clients.create')); ?>" class="add-client-btn">
                    <i class="fas fa-plus"></i>
                    إضافة عميل جديد
                </a>
                
                <!-- Excel Import/Export Section -->
                <div class="excel-controls">
                    <div class="btn-group">
                        <button type="button" class="btn btn-outline-success dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-file-excel"></i>
                            عمليات إكسيل
                        </button>
                        <ul class="dropdown-menu">
                            <li>
                                <a class="dropdown-item" href="<?php echo e(route('admin.clients.export')); ?>">
                                    <i class="fas fa-download text-primary"></i>
                                    تصدير إلى إكسيل
                                </a>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <a class="dropdown-item" href="<?php echo e(route('admin.clients.template')); ?>">
                                    <i class="fas fa-file-download text-info"></i>
                                    تحميل قالب الاستيراد
                                </a>
                            </li>
                            <li>
                                <button class="dropdown-item" type="button" onclick="triggerFileUpload()">
                                    <i class="fas fa-upload text-success"></i>
                                    استيراد من إكسيل
                                </button>
                            </li>
                        </ul>
                    </div>
                    
                    <!-- Direct import button for better visibility -->
                    <button type="button" class="btn btn-success" onclick="triggerFileUpload()" title="استيراد من إكسيل">
                        <i class="fas fa-file-upload"></i>
                        استيراد إكسيل
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Hidden import form -->
        <form id="importForm" action="<?php echo e(route('admin.clients.import')); ?>" method="POST" enctype="multipart/form-data" style="display: none;">
            <?php echo csrf_field(); ?>
            <input type="file" id="importFileInput" name="file" accept=".xlsx,.xls,.csv" onchange="handleFileImport()">
        </form>
    </div>

    <!-- Import Feedback -->
    <?php if(session('import_details')): ?>
        <div class="import-feedback">
            <div class="d-flex align-items-center">
                <i class="fas fa-file-excel text-success me-2"></i>
                <div>
                    <strong>نتائج الاستيراد:</strong>
                    تم استيراد <?php echo e(session('import_details.imported')); ?> عميل
                    <?php if(session('import_details.skipped') > 0): ?>
                        وتم تخطي <?php echo e(session('import_details.skipped')); ?> صف
                    <?php endif; ?>
                </div>
            </div>
            
            <?php if(!empty(session('import_details.errors'))): ?>
                <div class="details">
                    <strong>الأخطاء:</strong>
                    <ul class="mb-0 mt-2">
                        <?php $__currentLoopData = session('import_details.errors'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <li><?php echo e($error); ?></li>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </ul>
                </div>
            <?php endif; ?>
        </div>
    <?php endif; ?>

    <!-- Statistics Cards -->
    <div class="stats-grid">
        <div class="stat-card primary">
            <div class="stat-content">
                <div class="stat-info">
                    <h4><?php echo e($stats['total'] ?? 0); ?></h4>
                    <p>إجمالي العملاء</p>
                </div>
                <div class="stat-icon">
                    <i class="fas fa-users"></i>
                </div>
            </div>
        </div>
        
        <div class="stat-card success">
            <div class="stat-content">
                <div class="stat-info">
                    <h4><?php echo e($stats['active'] ?? 0); ?></h4>
                    <p>العملاء النشطون</p>
                </div>
                <div class="stat-icon">
                    <i class="fas fa-user-check"></i>
                </div>
            </div>
        </div>
        
        <div class="stat-card info">
            <div class="stat-content">
                <div class="stat-info">
                    <h4><?php echo e($stats['paid'] ?? 0); ?></h4>
                    <p>دفعوا المبلغ</p>
                </div>
                <div class="stat-icon">
                    <i class="fas fa-money-bill"></i>
                </div>
            </div>
        </div>
        
        <div class="stat-card warning">
            <div class="stat-content">
                <div class="stat-info">
                    <h4><?php echo e(number_format($stats['total_revenue'] ?? 0, 2)); ?></h4>
                    <p>إجمالي الإيرادات (ريال)</p>
                </div>
                <div class="stat-icon">
                    <i class="fas fa-chart-line"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="filter-card">
        <form method="GET" action="<?php echo e(route('admin.clients.index')); ?>" class="row g-3">
            <div class="col-md-3">
                <label class="form-label">البحث</label>
                <input type="text" name="search" class="form-control" placeholder="الاسم، الهاتف، الإيميل..." 
                       value="<?php echo e(request('search')); ?>">
            </div>
            
            <div class="col-md-2">
                <label class="form-label">المدينة</label>
                <select name="city" class="form-select">
                    <option value="">جميع المدن</option>
                    <?php $__currentLoopData = $cities; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $city): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <option value="<?php echo e($city); ?>" <?php echo e(request('city') == $city ? 'selected' : ''); ?>>
                            <?php echo e($city); ?>

                        </option>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </select>
            </div>
            
            <div class="col-md-2">
                <label class="form-label">الجنس</label>
                <select name="gender" class="form-select">
                    <option value="">الكل</option>
                    <option value="Male" <?php echo e(request('gender') == 'Male' ? 'selected' : ''); ?>>ذكر</option>
                    <option value="Female" <?php echo e(request('gender') == 'Female' ? 'selected' : ''); ?>>أنثى</option>
                </select>
            </div>
            
            <div class="col-md-2">
                <label class="form-label">الحالة</label>
                <select name="status" class="form-select">
                    <option value="">جميع الحالات</option>
                    <option value="active" <?php echo e(request('status') == 'active' ? 'selected' : ''); ?>>نشط</option>
                    <option value="inactive" <?php echo e(request('status') == 'inactive' ? 'selected' : ''); ?>>غير نشط</option>
                    <option value="pending" <?php echo e(request('status') == 'pending' ? 'selected' : ''); ?>>في الانتظار</option>
                </select>
            </div>
            
            <div class="col-md-2">
                <label class="form-label">حالة الدفع</label>
                <select name="payment_status" class="form-select">
                    <option value="">الكل</option>
                    <option value="paid" <?php echo e(request('payment_status') == 'paid' ? 'selected' : ''); ?>>مدفوع</option>
                    <option value="unpaid" <?php echo e(request('payment_status') == 'unpaid' ? 'selected' : ''); ?>>غير مدفوع</option>
                    <option value="partial" <?php echo e(request('payment_status') == 'partial' ? 'selected' : ''); ?>>مدفوع جزئياً</option>
                </select>
            </div>
            
            <div class="col-md-1">
                <label class="form-label">&nbsp;</label>
                <button type="submit" class="btn btn-primary w-100">
                    <i class="fas fa-search"></i>
                </button>
            </div>
        </form>
    </div>

    <!-- Dynamic Columns Management -->
    <?php if(!empty($dynamicFields)): ?>
        <div class="card mb-3" style="background: rgba(255, 107, 53, 0.05); border-color: rgba(255, 107, 53, 0.2);">
            <div class="card-header" style="background: rgba(255, 107, 53, 0.1); border-color: rgba(255, 107, 53, 0.2);">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="mb-0 text-warning">
                            <i class="fas fa-columns me-2"></i>
                            إدارة الأعمدة الديناميكية
                        </h6>
                        <small class="text-muted">تم إنشاء هذه الأعمدة تلقائياً من ملفات الإكسل المستوردة</small>
                    </div>
                    <button class="btn btn-sm btn-outline-warning" type="button" data-bs-toggle="collapse" data-bs-target="#dynamicColumnsManager">
                        <i class="fas fa-cog"></i>
                        إدارة الأعمدة
                    </button>
                </div>
            </div>
            <div class="collapse" id="dynamicColumnsManager">
                <div class="card-body">
                    <div class="row">
                        <?php $__currentLoopData = $dynamicFields; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $field): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="col-md-6 col-lg-4 mb-2">
                                <div class="d-flex justify-content-between align-items-center p-2 rounded" style="background: rgba(255, 255, 255, 0.05);">
                                    <span class="text-white">
                                        <i class="fas fa-th-list me-2 text-primary"></i>
                                        <?php echo e($field); ?>

                                    </span>
                                    <button class="btn btn-sm btn-outline-danger" onclick="removeDynamicField('<?php echo e($field); ?>')" title="حذف العمود">
                                        <i class="fas fa-trash-alt"></i>
                                    </button>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                    <div class="mt-3">
                        <div class="alert alert-warning" style="background: rgba(255, 193, 7, 0.1); border-color: rgba(255, 193, 7, 0.3); color: #ffc107;">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>تنبيه:</strong> حذف عمود سيؤدي إلى إزالة البيانات المرتبطة به من جميع العملاء نهائياً.
                        </div>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <!-- Clients Table -->
    <div class="clients-table-card">
        <div class="table-responsive">
            <table class="table">
                <thead>
                    <tr>
                        <th>العميل</th>
                        <th>معلومات الاتصال</th>
                        <th>التفاصيل</th>
                        <th>الحالة</th>
                        <th>حالة الدفع</th>
                        <th>المبلغ</th>
                        <?php if(!empty($dynamicFields)): ?>
                            <?php $__currentLoopData = $dynamicFields; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $field): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <th class="dynamic-field"><?php echo e($field); ?></th>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        <?php endif; ?>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    <?php $__empty_1 = true; $__currentLoopData = $clients; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $client): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <tr>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="client-avatar">
                                        <?php echo e(mb_substr($client->name ?? 'ع', 0, 1)); ?>

                                    </div>
                                    <div style="min-width: 120px;">
                                        <div class="client-name" style="color: #000000; font-weight: 600; margin-bottom: 2px;">
                                            <?php echo e($client->name ?? 'غير محدد'); ?>

                                        </div>
                                        <div class="client-contact" style="color: #666666; font-size: 0.85rem;">
                                            ID: <?php echo e($client->id); ?>

                                        </div>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div class="client-details" style="color: #000000;">
                                    <div><i class="fas fa-phone text-orange"></i> <?php echo e($client->phone); ?></div>
                                    <?php if($client->email): ?>
                                        <div><i class="fas fa-envelope text-orange"></i> <?php echo e($client->email); ?></div>
                                    <?php endif; ?>
                                </div>
                            </td>
                            <td>
                                <div class="client-details" style="color: #000000;">
                                    <div><i class="fas fa-map-marker-alt text-orange"></i> <?php echo e($client->city); ?>, <?php echo e($client->country); ?></div>
                                    <div><i class="fas fa-<?php echo e($client->gender == 'Male' ? 'mars' : 'venus'); ?> text-orange"></i> <?php echo e($client->gender == 'Male' ? 'ذكر' : 'أنثى'); ?>, <?php echo e($client->age); ?> سنة</div>
                                    <?php if($client->marital_status): ?>
                                        <div><i class="fas fa-heart text-orange"></i> <?php echo e($client->marital_status); ?></div>
                                    <?php endif; ?>
                                </div>
                            </td>
                            <td>
                                <div class="dropdown">
                                    <span class="status-badge status-<?php echo e($client->status ?? 'active'); ?> dropdown-toggle" 
                                          data-bs-toggle="dropdown" style="cursor: pointer;" title="انقر لتغيير الحالة">
                                        <?php if(($client->status ?? 'active') == 'active'): ?> نشط
                                        <?php elseif($client->status == 'inactive'): ?> غير نشط
                                        <?php else: ?> في الانتظار
                                        <?php endif; ?>
                                    </span>
                                    <div class="dropdown-menu">
                                        <button class="dropdown-item" onclick="updateClientStatus(<?php echo e($client->id); ?>, 'active')">
                                            <i class="fas fa-check-circle text-success"></i> نشط
                                        </button>
                                        <button class="dropdown-item" onclick="updateClientStatus(<?php echo e($client->id); ?>, 'inactive')">
                                            <i class="fas fa-times-circle text-danger"></i> غير نشط
                                        </button>
                                        <button class="dropdown-item" onclick="updateClientStatus(<?php echo e($client->id); ?>, 'pending')">
                                            <i class="fas fa-clock text-warning"></i> في الانتظار
                                        </button>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <span class="status-badge payment-<?php echo e($client->payment_status); ?>">
                                    <?php if($client->payment_status == 'paid'): ?> مدفوع
                                    <?php elseif($client->payment_status == 'unpaid'): ?> غير مدفوع
                                    <?php else: ?> مدفوع جزئياً
                                    <?php endif; ?>
                                </span>
                            </td>
                            <td>
                                <div class="text-black font-weight-bold"><?php echo e(number_format($client->amount_paid, 2)); ?> ريال</div>
                                <?php if($client->payment_method): ?>
                                    <small class="text-secondary"><?php echo e($client->payment_method); ?></small>
                                <?php endif; ?>
                            </td>
                            <?php if(!empty($dynamicFields)): ?>
                                <?php $__currentLoopData = $dynamicFields; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $field): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <td class="dynamic-field-value">
                                        <?php
                                            $fieldValue = $client->getFormattedDynamicField($field);
                                        ?>
                                        <?php if($fieldValue !== '-'): ?>
                                            <span class="text-black" title="<?php echo e($fieldValue); ?>"><?php echo e($fieldValue); ?></span>
                                        <?php else: ?>
                                            <span class="text-muted">-</span>
                                        <?php endif; ?>
                                    </td>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            <?php endif; ?>
                            <td>
                                <div class="action-buttons">
                                    <a href="<?php echo e(route('admin.clients.show', $client)); ?>" class="btn btn-outline-primary btn-sm" title="عرض">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="<?php echo e(route('admin.clients.edit', $client)); ?>" class="btn btn-outline-success btn-sm" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <form method="POST" action="<?php echo e(route('admin.clients.destroy', $client)); ?>" class="d-inline">
                                        <?php echo csrf_field(); ?>
                                        <?php echo method_field('DELETE'); ?>
                                        <button type="submit" class="btn btn-outline-danger btn-sm" 
                                                onclick="return confirm('هل أنت متأكد من حذف هذا العميل؟')" title="حذف">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <tr>
                            <td colspan="<?php echo e(7 + count($dynamicFields ?? [])); ?>" class="text-center text-secondary py-5">
                                <i class="fas fa-users fa-3x mb-3 text-orange"></i>
                                <div>لا توجد عملاء مطابقون للمعايير المحددة</div>
                            </td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>

    <!-- Pagination -->
    <?php if(method_exists($clients, 'links')): ?>
        <div class="d-flex justify-content-center">
            <?php echo e($clients->links()); ?>

        </div>
    <?php endif; ?>
</div>

<?php $__env->startPush('scripts'); ?>
<script>
// Function to trigger file upload
function triggerFileUpload() {
    document.getElementById('importFileInput').click();
}

// Function to handle file import
function handleFileImport() {
    const fileInput = document.getElementById('importFileInput');
    const file = fileInput.files[0];
    
    if (file) {
        // Validate file type
        const allowedTypes = ['.xlsx', '.xls', '.csv'];
        const fileExtension = '.' + file.name.split('.').pop().toLowerCase();
        
        if (!allowedTypes.includes(fileExtension)) {
            Swal.fire({
                icon: 'error',
                title: 'نوع ملف غير صحيح',
                text: 'يرجى اختيار ملف Excel (.xlsx, .xls) أو CSV (.csv)',
                background: 'rgba(33, 37, 41, 0.95)',
                color: '#fff'
            });
            return;
        }

        // Validate file size (5MB max)
        if (file.size > 5 * 1024 * 1024) {
            Swal.fire({
                icon: 'error',
                title: 'حجم الملف كبير جداً',
                text: 'يرجى اختيار ملف أقل من 5 ميجابايت',
                background: 'rgba(33, 37, 41, 0.95)',
                color: '#fff'
            });
            return;
        }

        // Show loading indicator
        Swal.fire({
            title: 'جاري استيراد البيانات...',
            html: `
                <div class="d-flex flex-column align-items-center">
                    <div class="spinner-border text-primary mb-3" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p>يتم الآن معالجة ملف: <strong>${file.name}</strong></p>
                    <small class="text-muted">يرجى الانتظار حتى اكتمال العملية...</small>
                </div>
            `,
            allowOutsideClick: false,
            showConfirmButton: false,
            background: 'rgba(33, 37, 41, 0.95)',
            color: '#fff'
        });

        // Submit the form
        document.getElementById('importForm').submit();
    }
}

// Add confirmation for delete actions
document.addEventListener('DOMContentLoaded', function() {
    const deleteButtons = document.querySelectorAll('form[action*="destroy"] button[type="submit"]');
    
    deleteButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            
            Swal.fire({
                title: 'هل أنت متأكد؟',
                text: 'لن تتمكن من التراجع عن هذا الإجراء!',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#d33',
                cancelButtonColor: '#3085d6',
                confirmButtonText: 'نعم، احذف!',
                cancelButtonText: 'إلغاء',
                background: 'rgba(33, 37, 41, 0.95)',
                color: '#fff'
            }).then((result) => {
                if (result.isConfirmed) {
                    this.closest('form').submit();
                }
            });
        });
    });
});

// Enhanced search functionality
function enhancedSearch() {
    const searchForm = document.querySelector('form[action*="clients.index"]');
    if (searchForm) {
        searchForm.addEventListener('submit', function(e) {
            // Show loading state
            const submitButton = this.querySelector('button[type="submit"]');
            const originalHTML = submitButton.innerHTML;
            submitButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
            submitButton.disabled = true;
            
            setTimeout(() => {
                submitButton.innerHTML = originalHTML;
                submitButton.disabled = false;
            }, 2000);
        });
    }
}

enhancedSearch();

// Remove dynamic field function
function removeDynamicField(fieldName) {
    Swal.fire({
        title: 'هل أنت متأكد؟',
        html: `
            <p>سيتم حذف العمود "<strong>${fieldName}</strong>" نهائياً من جميع العملاء.</p>
            <p class="text-danger">هذا الإجراء لا يمكن التراجع عنه!</p>
        `,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#3085d6',
        confirmButtonText: 'نعم، احذف العمود!',
        cancelButtonText: 'إلغاء',
        background: 'rgba(33, 37, 41, 0.95)',
        color: '#fff'
    }).then((result) => {
        if (result.isConfirmed) {
            fetch('/admin/clients/remove-dynamic-field', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({ field_name: fieldName })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    Swal.fire({
                        icon: 'success',
                        title: 'تم الحذف بنجاح',
                        text: data.message,
                        timer: 3000,
                        showConfirmButton: false,
                        background: 'rgba(33, 37, 41, 0.95)',
                        color: '#fff'
                    });
                    
                    // Refresh the page to show updated columns
                    setTimeout(() => {
                        window.location.reload();
                    }, 2000);
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'خطأ',
                        text: data.message || 'حدث خطأ في الحذف',
                        background: 'rgba(33, 37, 41, 0.95)',
                        color: '#fff'
                    });
                }
            })
            .catch(error => {
                console.error('Error:', error);
                Swal.fire({
                    icon: 'error',
                    title: 'خطأ',
                    text: 'حدث خطأ في الاتصال',
                    background: 'rgba(33, 37, 41, 0.95)',
                    color: '#fff'
                });
            });
        }
    });
}

// Update client status function
function updateClientStatus(clientId, status) {
    fetch(`/admin/clients/${clientId}/status`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({ status: status })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Show success message
            Swal.fire({
                icon: 'success',
                title: 'تم التحديث بنجاح',
                text: data.message,
                timer: 2000,
                showConfirmButton: false,
                background: 'rgba(33, 37, 41, 0.95)',
                color: '#fff'
            });
            
            // Refresh the page to show updated status
            setTimeout(() => {
                window.location.reload();
            }, 1500);
        } else {
            Swal.fire({
                icon: 'error',
                title: 'خطأ',
                text: data.message || 'حدث خطأ في التحديث',
                background: 'rgba(33, 37, 41, 0.95)',
                color: '#fff'
            });
        }
    })
    .catch(error => {
        console.error('Error:', error);
        Swal.fire({
            icon: 'error',
            title: 'خطأ',
            text: 'حدث خطأ في الاتصال',
            background: 'rgba(33, 37, 41, 0.95)',
            color: '#fff'
        });
    });
}
</script>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?> 
<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\GiftsSaudi-Laravel (1)\resources\views\admin\clients\index.blade.php ENDPATH**/ ?>