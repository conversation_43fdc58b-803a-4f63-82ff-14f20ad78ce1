<div class="client-filtering-stage">
    <div class="stage-intro mb-4">
        <h5 class="stage-title">حدد العملاء المستهدفين</h5>
        <p class="stage-description">استخدم الفلاتر أدناه لتحديد العملاء الذين تريد استهدافهم في هذه الحملة</p>
    </div>

    <div class="filtering-options">
        <div class="row">
            <!-- Age Range Filter -->
            <div class="col-md-6">
                <div class="filter-section">
                    <h6 class="filter-title">
                        <i class="fas fa-birthday-cake me-2"></i>
                        العمر
                    </h6>
                    <div class="row">
                        <div class="col-6">
                            <label for="age_from" class="form-label">من</label>
                            <input type="number" 
                                   id="age_from" 
                                   name="age_from" 
                                   class="form-control"
                                   value="<?php echo e(old('age_from', $campaign->client_filters['age_from'] ?? '')); ?>"
                                   placeholder="18"
                                   min="1" 
                                   max="100">
                        </div>
                        <div class="col-6">
                            <label for="age_to" class="form-label">إلى</label>
                            <input type="number" 
                                   id="age_to" 
                                   name="age_to" 
                                   class="form-control"
                                   value="<?php echo e(old('age_to', $campaign->client_filters['age_to'] ?? '')); ?>"
                                   placeholder="65"
                                   min="1" 
                                   max="100">
                        </div>
                    </div>
                </div>
            </div>

            <!-- Gender Filter -->
            <div class="col-md-6">
                <div class="filter-section">
                    <h6 class="filter-title">
                        <i class="fas fa-venus-mars me-2"></i>
                        الجنس
                    </h6>
                    <select id="gender" 
                            name="gender" 
                            class="form-select">
                        <option value="">جميع الأجناس</option>
                        <option value="male" <?php echo e(old('gender', $campaign->client_filters['gender'] ?? '') == 'male' ? 'selected' : ''); ?>>ذكر</option>
                        <option value="female" <?php echo e(old('gender', $campaign->client_filters['gender'] ?? '') == 'female' ? 'selected' : ''); ?>>أنثى</option>
                    </select>
                </div>
            </div>
        </div>

        <div class="row mt-3">
            <!-- City Filter -->
            <div class="col-md-6">
                <div class="filter-section">
                    <h6 class="filter-title">
                        <i class="fas fa-city me-2"></i>
                        المدينة
                    </h6>
                    <select id="city" 
                            name="city" 
                            class="form-select">
                        <option value="">جميع المدن</option>
                        <?php
                            $cities = ['الرياض', 'جدة', 'الدمام', 'مكة المكرمة', 'المدينة المنورة', 'تبوك', 'أبها', 'الطائف', 'بريدة', 'حائل'];
                        ?>
                        <?php $__currentLoopData = $cities; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $city): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($city); ?>" <?php echo e(old('city', $campaign->client_filters['city'] ?? '') == $city ? 'selected' : ''); ?>><?php echo e($city); ?></option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                </div>
            </div>

            <!-- Country Filter -->
            <div class="col-md-6">
                <div class="filter-section">
                    <h6 class="filter-title">
                        <i class="fas fa-globe me-2"></i>
                        الدولة
                    </h6>
                    <select id="country" 
                            name="country" 
                            class="form-select">
                        <option value="">جميع الدول</option>
                        <?php
                            $countries = ['السعودية', 'الإمارات', 'الكويت', 'قطر', 'البحرين', 'عمان', 'الأردن', 'لبنان', 'مصر', 'العراق'];
                        ?>
                        <?php $__currentLoopData = $countries; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $country): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($country); ?>" <?php echo e(old('country', $campaign->client_filters['country'] ?? '') == $country ? 'selected' : ''); ?>><?php echo e($country); ?></option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                </div>
            </div>
        </div>
    </div>

    <!-- Preview Section -->
    <div class="preview-section mt-4">
        <div class="card preview-card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-eye me-2"></i>
                    معاينة العملاء المستهدفين
                </h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-md-4">
                        <div class="preview-stat">
                            <div class="stat-number" id="total-clients"><?php echo e($filteredClientsCount); ?></div>
                            <div class="stat-label">إجمالي العملاء</div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="preview-stat">
                            <div class="stat-number" id="filtered-clients"><?php echo e($filteredClientsCount); ?></div>
                            <div class="stat-label">العملاء المفلترين</div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="preview-stat">
                            <div class="stat-number" id="estimated-cost"><?php echo e(number_format(($campaign->gift->price ?? 0) * $filteredClientsCount, 2)); ?></div>
                            <div class="stat-label">التكلفة المتوقعة (ريال)</div>
                        </div>
                    </div>
                </div>

                <div class="filter-summary mt-3" id="filter-summary">
                    <h6 class="summary-title">
                        <i class="fas fa-filter me-2"></i>
                        ملخص الفلاتر المطبقة
                    </h6>
                    <div class="summary-content">
                        <span class="summary-item" id="no-filters" style="display: none;">لم يتم تطبيق أي فلاتر - سيتم استهداف جميع العملاء</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Action Buttons -->
    <div class="filter-actions mt-4">
        <div class="d-flex justify-content-between align-items-center">
            <button type="button" class="btn btn-outline-secondary" id="clear-filters">
                <i class="fas fa-eraser me-2"></i>
                مسح جميع الفلاتر
            </button>
            
            <button type="button" class="btn btn-info" id="refresh-preview">
                <i class="fas fa-sync-alt me-2"></i>
                تحديث المعاينة
            </button>
        </div>
    </div>
</div>

<style>
.client-filtering-stage {
    padding: 1rem 0;
}

.stage-intro {
    text-align: center;
    margin-bottom: 2rem;
}

.stage-title {
    color: #333;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.stage-description {
    color: #6c757d;
    font-size: 1rem;
}

.filter-section {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 10px;
    border-left: 4px solid #667eea;
    height: 100%;
}

.filter-title {
    color: #333;
    font-weight: 600;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
}

.filter-title i {
    color: #667eea;
}

.form-control, .form-select {
    border-radius: 8px;
    border: 2px solid #e9ecef;
    padding: 0.75rem;
    transition: all 0.3s ease;
}

.form-control:focus, .form-select:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.preview-card {
    border: none;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    border-radius: 15px;
}

.preview-card .card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 15px 15px 0 0;
}

.preview-stat {
    padding: 1rem;
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: #667eea;
    margin-bottom: 0.5rem;
}

.stat-label {
    color: #6c757d;
    font-weight: 500;
}

.filter-summary {
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 10px;
    border-left: 4px solid #17a2b8;
}

.summary-title {
    color: #333;
    font-weight: 600;
    margin-bottom: 0.75rem;
}

.summary-title i {
    color: #17a2b8;
}

.summary-content {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.summary-item {
    background: #667eea;
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 500;
}

.filter-actions {
    border-top: 2px solid #e9ecef;
    padding-top: 1.5rem;
}

.btn {
    border-radius: 8px;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-2px);
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const giftPrice = <?php echo e($campaign->gift->price ?? 0); ?>;
    const totalClientsCount = <?php echo e($filteredClientsCount); ?>;
    
    const ageFromInput = document.getElementById('age_from');
    const ageToInput = document.getElementById('age_to');
    const genderSelect = document.getElementById('gender');
    const citySelect = document.getElementById('city');
    const countrySelect = document.getElementById('country');
    
    const totalClientsElement = document.getElementById('total-clients');
    const filteredClientsElement = document.getElementById('filtered-clients');
    const estimatedCostElement = document.getElementById('estimated-cost');
    const filterSummary = document.getElementById('filter-summary');
    const noFiltersElement = document.getElementById('no-filters');
    
    const clearFiltersBtn = document.getElementById('clear-filters');
    const refreshPreviewBtn = document.getElementById('refresh-preview');

    function updatePreview() {
        // Collect active filters for display
        const filters = [];

        if (ageFromInput.value || ageToInput.value) {
            const ageFrom = ageFromInput.value || 'غير محدد';
            const ageTo = ageToInput.value || 'غير محدد';
            filters.push(`العمر: ${ageFrom} - ${ageTo}`);
        }

        if (genderSelect.value) {
            const genderText = genderSelect.value === 'male' ? 'ذكر' : 'أنثى';
            filters.push(`الجنس: ${genderText}`);
        }

        if (citySelect.value) {
            filters.push(`المدينة: ${citySelect.value}`);
        }

        if (countrySelect.value) {
            filters.push(`الدولة: ${countrySelect.value}`);
        }

        // Update filter summary
        const summaryContent = filterSummary.querySelector('.summary-content');
        summaryContent.innerHTML = '';

        if (filters.length === 0) {
            summaryContent.appendChild(noFiltersElement.cloneNode(true));
            summaryContent.firstChild.style.display = 'inline-block';
        } else {
            filters.forEach(filter => {
                const filterElement = document.createElement('span');
                filterElement.className = 'summary-item';
                filterElement.textContent = filter;
                summaryContent.appendChild(filterElement);
            });
        }

        // Get real count from server
        fetchFilteredCount();
    }

    function fetchFilteredCount() {
        // Show loading state
        filteredClientsElement.textContent = '...';
        estimatedCostElement.textContent = '...';

        // Prepare form data
        const formData = new FormData();
        formData.append('_token', document.querySelector('meta[name="csrf-token"]').getAttribute('content'));
        
        if (ageFromInput.value) formData.append('age_from', ageFromInput.value);
        if (ageToInput.value) formData.append('age_to', ageToInput.value);
        if (genderSelect.value) formData.append('gender', genderSelect.value);
        if (citySelect.value) formData.append('city', citySelect.value);
        if (countrySelect.value) formData.append('country', countrySelect.value);

        // Make API request
        fetch('<?php echo e(route("vendor.campaigns.filtered-clients-count")); ?>', {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            const filteredCount = data.count;
            filteredClientsElement.textContent = filteredCount;
            
            const estimatedCost = giftPrice * filteredCount;
            estimatedCostElement.textContent = estimatedCost.toLocaleString();
        })
        .catch(error => {
            console.error('Error:', error);
            filteredClientsElement.textContent = '0';
            estimatedCostElement.textContent = '0';
        });
    }

    function clearAllFilters() {
        ageFromInput.value = '';
        ageToInput.value = '';
        genderSelect.value = '';
        citySelect.value = '';
        countrySelect.value = '';
        updatePreview();
    }

    // Event listeners
    [ageFromInput, ageToInput, genderSelect, citySelect, countrySelect].forEach(element => {
        element.addEventListener('change', updatePreview);
        element.addEventListener('input', updatePreview);
    });

    clearFiltersBtn.addEventListener('click', clearAllFilters);
    refreshPreviewBtn.addEventListener('click', updatePreview);

    // Initial update
    updatePreview();

    // Add validation for age range
    ageFromInput.addEventListener('input', function() {
        const ageFrom = parseInt(this.value);
        const ageTo = parseInt(ageToInput.value);
        
        if (ageFrom && ageTo && ageFrom > ageTo) {
            ageToInput.value = ageFrom;
        }
    });

    ageToInput.addEventListener('input', function() {
        const ageFrom = parseInt(ageFromInput.value);
        const ageTo = parseInt(this.value);
        
        if (ageFrom && ageTo && ageTo < ageFrom) {
            ageFromInput.value = ageTo;
        }
    });
});
</script> <?php /**PATH D:\GiftsSaudi-Laravel (1)\resources\views/vendor/campaigns/stages/client-filtering.blade.php ENDPATH**/ ?>