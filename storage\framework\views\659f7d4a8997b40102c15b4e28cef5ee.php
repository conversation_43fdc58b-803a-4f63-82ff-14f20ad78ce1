<div class="template-selection-stage">
    <div class="stage-intro mb-4">
        <h5 class="stage-title">اختر قالب الرسالة لحملتك</h5>
        <p class="stage-description">يمكنك اختيار قالب جاهز أو إنشاء رسالة مخصصة تناسب هدف الحملة</p>
    </div>

    <div class="template-options">
        <!-- Template Type Selection -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="template-option-card">
                    <input type="radio" 
                           id="prebuilt_template" 
                           name="template_type" 
                           value="prebuilt" 
                           class="template-radio"
                           <?php echo e(old('template_type', $campaign->template_type) == 'prebuilt' ? 'checked' : ''); ?>>
                    <label for="prebuilt_template" class="template-option-label">
                        <div class="template-card">
                            <div class="template-icon">
                                <i class="fas fa-list-alt"></i>
                            </div>
                            <h6 class="template-title">قوالب جاهزة</h6>
                            <p class="template-desc">اختر من مجموعة القوالب المعدة مسبقاً</p>
                            <div class="template-features">
                                <span class="feature-badge">سهل الاستخدام</span>
                                <span class="feature-badge">مُجرب ومُحسّن</span>
                            </div>
                        </div>
                    </label>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="template-option-card">
                    <input type="radio" 
                           id="custom_template" 
                           name="template_type" 
                           value="custom" 
                           class="template-radio"
                           <?php echo e(old('template_type', $campaign->template_type) == 'custom' ? 'checked' : ''); ?>>
                    <label for="custom_template" class="template-option-label">
                        <div class="template-card">
                            <div class="template-icon">
                                <i class="fas fa-edit"></i>
                            </div>
                            <h6 class="template-title">رسالة مخصصة</h6>
                            <p class="template-desc">أنشئ رسالة فريدة تناسب علامتك التجارية</p>
                            <div class="template-features">
                                <span class="feature-badge">مرونة كاملة</span>
                                <span class="feature-badge">إبداع لا محدود</span>
                            </div>
                        </div>
                    </label>
                </div>
            </div>
        </div>

        <!-- Prebuilt Templates Section -->
        <div id="prebuilt-section" class="template-section" style="display: none;">
            <h6 class="section-title mb-3">
                <i class="fas fa-list-alt me-2"></i>
                اختر من القوالب الجاهزة
            </h6>
            
            <div class="templates-grid">
                <?php $__empty_1 = true; $__currentLoopData = $messageTemplates; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $template): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                    <div class="template-item">
                        <input type="radio" 
                               id="template_<?php echo e($template->id); ?>" 
                               name="message_template_id" 
                               value="<?php echo e($template->id); ?>"
                               class="template-item-radio"
                               <?php echo e(old('message_template_id', $campaign->message_template_id) == $template->id ? 'checked' : ''); ?>>
                        <label for="template_<?php echo e($template->id); ?>" class="template-item-label">
                            <div class="template-preview">
                                <div class="template-header">
                                    <h6 class="template-name"><?php echo e($template->name); ?></h6>
                                    <span class="template-type-badge"><?php echo e($template->type); ?></span>
                                </div>
                                <div class="template-content">
                                    <p class="template-message"><?php echo e(Str::limit($template->message, 100)); ?></p>
                                </div>
                                <?php if($template->media_type): ?>
                                    <div class="template-media">
                                        <i class="fas fa-<?php echo e($template->media_type == 'image' ? 'image' : ($template->media_type == 'video' ? 'video' : 'volume-up')); ?> me-1"></i>
                                        يحتوي على <?php echo e($template->media_type == 'image' ? 'صورة' : ($template->media_type == 'video' ? 'فيديو' : 'صوت')); ?>

                                    </div>
                                <?php endif; ?>
                            </div>
                        </label>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                    <div class="col-12">
                        <div class="alert alert-info text-center">
                            <i class="fas fa-info-circle me-2"></i>
                            لا توجد قوالب متاحة حالياً
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Custom Template Section -->
        <div id="custom-section" class="template-section" style="display: none;">
            <h6 class="section-title mb-3">
                <i class="fas fa-edit me-2"></i>
                أنشئ رسالتك المخصصة
            </h6>
            
            <div class="custom-template-form">
                <div class="row">
                    <div class="col-md-12">
                        <div class="form-group mb-3">
                            <label for="custom_message" class="form-label">
                                <i class="fas fa-comment-dots me-1"></i>
                                نص الرسالة *
                            </label>
                            <textarea id="custom_message" 
                                      name="custom_message" 
                                      class="form-control <?php $__errorArgs = ['custom_message'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                      rows="8"
                                      placeholder="اكتب نص رسالتك هنا... يمكنك استخدام المتغيرات مثل [اسم_العميل] و [اسم_الهدية]"><?php echo e(old('custom_message', $campaign->custom_message)); ?></textarea>
                            <?php $__errorArgs = ['custom_message'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            <div class="form-text">
                                <i class="fas fa-lightbulb me-1"></i>
                                نصائح: استخدم [اسم_العميل] و [اسم_الهدية] ليتم استبدالهما تلقائياً
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group mb-3">
                            <label for="custom_media_type" class="form-label">
                                <i class="fas fa-file-upload me-1"></i>
                                نوع الوسائط (اختياري)
                            </label>
                            <select id="custom_media_type" 
                                    name="custom_media_type" 
                                    class="form-select <?php $__errorArgs = ['custom_media_type'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>">
                                <option value="">بدون وسائط</option>
                                <option value="image" <?php echo e(old('custom_media_type', $campaign->custom_media_type) == 'image' ? 'selected' : ''); ?>>صورة</option>
                                <option value="video" <?php echo e(old('custom_media_type', $campaign->custom_media_type) == 'video' ? 'selected' : ''); ?>>فيديو</option>
                                <option value="audio" <?php echo e(old('custom_media_type', $campaign->custom_media_type) == 'audio' ? 'selected' : ''); ?>>صوت</option>
                            </select>
                            <?php $__errorArgs = ['custom_media_type'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="form-group mb-3" id="media-upload-section" style="display: none;">
                            <label for="custom_media_file" class="form-label">
                                <i class="fas fa-cloud-upload-alt me-1"></i>
                                رفع الملف
                            </label>
                            <input type="file" 
                                   id="custom_media_file" 
                                   name="custom_media_file" 
                                   class="form-control <?php $__errorArgs = ['custom_media_file'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                   accept="">
                            <?php $__errorArgs = ['custom_media_file'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            <div class="form-text">
                                الحد الأقصى: 10 ميجابايت
                            </div>
                        </div>
                    </div>
                </div>

                <?php if($campaign->custom_media_path): ?>
                    <div class="current-media mb-3">
                        <div class="alert alert-info">
                            <i class="fas fa-file me-2"></i>
                            الملف الحالي: <a href="<?php echo e($campaign->custom_media_path); ?>" target="_blank">عرض الملف</a>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<style>
.template-option-card {
    height: 100%;
}

.template-radio {
    display: none;
}

.template-option-label {
    display: block;
    cursor: pointer;
    height: 100%;
}

.template-card {
    border: 2px solid #e9ecef;
    border-radius: 15px;
    padding: 2rem;
    text-align: center;
    transition: all 0.3s ease;
    height: 100%;
    background: white;
}

.template-radio:checked + .template-option-label .template-card {
    border-color: #667eea;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.05) 100%);
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.2);
}

.template-icon {
    font-size: 3rem;
    color: #667eea;
    margin-bottom: 1rem;
}

.template-title {
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #333;
}

.template-desc {
    color: #6c757d;
    margin-bottom: 1rem;
}

.template-features {
    display: flex;
    gap: 0.5rem;
    justify-content: center;
    flex-wrap: wrap;
}

.feature-badge {
    background: #667eea;
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 500;
}

.template-section {
    margin-top: 2rem;
    padding: 1.5rem;
    background: #f8f9fa;
    border-radius: 15px;
    border-left: 4px solid #667eea;
}

.section-title {
    color: #333;
    font-weight: 600;
}

.templates-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1rem;
}

.template-item {
    position: relative;
}

.template-item-radio {
    display: none;
}

.template-item-label {
    display: block;
    cursor: pointer;
}

.template-preview {
    border: 2px solid #e9ecef;
    border-radius: 10px;
    padding: 1rem;
    background: white;
    transition: all 0.3s ease;
}

.template-item-radio:checked + .template-item-label .template-preview {
    border-color: #667eea;
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.2);
}

.template-header {
    display: flex;
    justify-content: between;
    align-items: center;
    margin-bottom: 0.75rem;
}

.template-name {
    font-weight: 600;
    margin: 0;
    flex: 1;
}

.template-type-badge {
    background: #e9ecef;
    color: #495057;
    padding: 0.25rem 0.5rem;
    border-radius: 15px;
    font-size: 0.75rem;
}

.template-content {
    margin-bottom: 0.75rem;
}

.template-message {
    color: #6c757d;
    line-height: 1.5;
    margin: 0;
}

.template-media {
    background: #f8f9fa;
    padding: 0.5rem;
    border-radius: 5px;
    font-size: 0.875rem;
    color: #495057;
}

.custom-template-form {
    background: white;
    padding: 1.5rem;
    border-radius: 10px;
    border: 1px solid #e9ecef;
}

.current-media {
    background: white;
    padding: 1rem;
    border-radius: 10px;
    border: 1px solid #e9ecef;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const templateRadios = document.querySelectorAll('input[name="template_type"]');
    const prebuiltSection = document.getElementById('prebuilt-section');
    const customSection = document.getElementById('custom-section');
    const mediaTypeSelect = document.getElementById('custom_media_type');
    const mediaUploadSection = document.getElementById('media-upload-section');
    const mediaFileInput = document.getElementById('custom_media_file');

    function toggleSections() {
        const selectedType = document.querySelector('input[name="template_type"]:checked')?.value;
        
        if (selectedType === 'prebuilt') {
            prebuiltSection.style.display = 'block';
            customSection.style.display = 'none';
        } else if (selectedType === 'custom') {
            prebuiltSection.style.display = 'none';
            customSection.style.display = 'block';
        } else {
            prebuiltSection.style.display = 'none';
            customSection.style.display = 'none';
        }
    }

    function updateMediaUpload() {
        const mediaType = mediaTypeSelect.value;
        if (mediaType) {
            mediaUploadSection.style.display = 'block';
            
            // Update file accept attribute based on media type
            let acceptTypes = '';
            switch(mediaType) {
                case 'image':
                    acceptTypes = 'image/*';
                    break;
                case 'video':
                    acceptTypes = 'video/*';
                    break;
                case 'audio':
                    acceptTypes = 'audio/*';
                    break;
            }
            mediaFileInput.setAttribute('accept', acceptTypes);
        } else {
            mediaUploadSection.style.display = 'none';
        }
    }

    // Event listeners
    templateRadios.forEach(radio => {
        radio.addEventListener('change', toggleSections);
    });

    mediaTypeSelect.addEventListener('change', updateMediaUpload);

    // Initial setup
    toggleSections();
    updateMediaUpload();
});
</script> <?php /**PATH D:\GiftsSaudi-Laravel (1)\resources\views/vendor/campaigns/stages/template-selection.blade.php ENDPATH**/ ?>