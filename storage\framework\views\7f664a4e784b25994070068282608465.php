<div class="form-section">
    <h3 class="section-title">
        <i class="fas fa-gift stage-icon"></i>
        اختيار الهدية للحملة
    </h3>
    
    <div class="row">
        <div class="col-md-12">
            <label for="gift_id" class="form-label">الهدية المراد الترويج لها</label>
            <select name="gift_id" id="gift_id" class="form-select" required>
                <option value="">اختر الهدية...</option>
                <?php $__currentLoopData = $gifts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $gift): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <option value="<?php echo e($gift->id); ?>" <?php echo e($campaign->gift_id == $gift->id ? 'selected' : ''); ?>>
                        <?php echo e($gift->name); ?> - <?php echo e(number_format($gift->price)); ?> ريال
                    </option>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </select>
        </div>
    </div>

    <?php if($campaign->gift): ?>
        <div class="mt-4 p-4 rounded" style="background: rgba(42, 42, 42, 0.6); border: 1px solid #555;">
            <h5 class="text-white mb-3">معاينة الهدية المختارة</h5>
            <div class="row">
                <div class="col-md-3">
                    <?php if($campaign->gift->image): ?>
                        <img src="<?php echo e(asset('storage/' . $campaign->gift->image)); ?>" 
                             alt="<?php echo e($campaign->gift->name); ?>" 
                             class="img-fluid rounded"
                             style="max-height: 200px; width: 100%; object-fit: cover;">
                    <?php else: ?>
                        <div class="bg-secondary d-flex align-items-center justify-content-center rounded" 
                             style="height: 200px;">
                            <i class="fas fa-gift fa-3x text-muted"></i>
                        </div>
                    <?php endif; ?>
                </div>
                <div class="col-md-9">
                    <h4 class="text-white"><?php echo e($campaign->gift->name); ?></h4>
                    <p class="text-muted"><?php echo e($campaign->gift->description); ?></p>
                    <div class="row mt-3">
                        <div class="col-sm-6">
                            <strong class="text-orange">السعر:</strong>
                            <span class="text-white"><?php echo e(number_format($campaign->gift->price)); ?> ريال</span>
                        </div>
                        <div class="col-sm-6">
                            <strong class="text-orange">المورد:</strong>
                            <span class="text-white"><?php echo e($campaign->gift->vendor->business_name ?? 'غير محدد'); ?></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>
</div>

<div class="form-section">
    <h3 class="section-title">
        <i class="fas fa-bullhorn stage-icon"></i>
        تفاصيل الحملة
    </h3>
    
    <div class="row">
        <div class="col-md-6">
            <label for="campaign_name" class="form-label">اسم الحملة</label>
            <input type="text" name="campaign_name" id="campaign_name" 
                   class="form-control" 
                   value="<?php echo e($campaign->campaign_name); ?>" 
                   placeholder="أدخل اسم مميز للحملة"
                   required>
        </div>
        <div class="col-md-6">
            <label for="campaign_type" class="form-label">نوع الحملة</label>
            <select name="campaign_type" id="campaign_type" class="form-select" required>
                <option value="">اختر نوع الحملة...</option>
                <option value="promotional" <?php echo e($campaign->campaign_type == 'promotional' ? 'selected' : ''); ?>>
                    ترويجية
                </option>
                <option value="seasonal" <?php echo e($campaign->campaign_type == 'seasonal' ? 'selected' : ''); ?>>
                    موسمية
                </option>
                <option value="loyalty" <?php echo e($campaign->campaign_type == 'loyalty' ? 'selected' : ''); ?>>
                    ولاء العملاء
                </option>
                <option value="birthday" <?php echo e($campaign->campaign_type == 'birthday' ? 'selected' : ''); ?>>
                    أعياد ميلاد
                </option>
                <option value="appreciation" <?php echo e($campaign->campaign_type == 'appreciation' ? 'selected' : ''); ?>>
                    تقديرية
                </option>
            </select>
        </div>
    </div>

    <div class="row mt-3">
        <div class="col-md-12">
            <label for="campaign_description" class="form-label">وصف الحملة</label>
            <textarea name="campaign_description" id="campaign_description" 
                      class="form-control" 
                      rows="4" 
                      placeholder="وصف مختصر عن أهداف الحملة..."><?php echo e($campaign->campaign_description); ?></textarea>
        </div>
    </div>
</div> <?php /**PATH D:\GiftsSaudi-Laravel (1)\resources\views\admin\campaigns\stages\gift-info.blade.php ENDPATH**/ ?>