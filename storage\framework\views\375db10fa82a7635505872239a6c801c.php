<div class="platform-selection-stage">
    <div class="stage-intro mb-4">
        <h5 class="stage-title">اختر منصة الإرسال</h5>
        <p class="stage-description">حدد المنصة التي ستستخدمها لإرسال الرسائل وقناة التواصل المفضلة</p>
    </div>

    <div class="platform-selection-section">
        <!-- Platform Type Selection -->
        <div class="platform-options mb-4">
            <h6 class="section-title">
                <i class="fas fa-server me-2"></i>
                نوع المنصة
            </h6>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="platform-option-card">
                        <input type="radio" 
                               id="own_platform" 
                               name="platform" 
                               value="own" 
                               class="platform-radio"
                               <?php echo e(old('platform', $campaign->platform) == 'own' ? 'checked' : ''); ?>>
                        <label for="own_platform" class="platform-option-label">
                            <div class="platform-card">
                                <div class="platform-icon">
                                    <i class="fas fa-home"></i>
                                </div>
                                <h6 class="platform-title">منصتنا الخاصة</h6>
                                <p class="platform-desc">استخدم منصة GiftsSaudi المدمجة</p>
                                <div class="platform-features">
                                    <span class="feature-badge">مجاني</span>
                                    <span class="feature-badge">سهل الاستخدام</span>
                                    <span class="feature-badge">آمن</span>
                                </div>
                                <div class="platform-pricing">
                                    <span class="price">0.50 ريال</span>
                                    <small>لكل رسالة</small>
                                </div>
                            </div>
                        </label>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="platform-option-card">
                        <input type="radio" 
                               id="external_platform" 
                               name="platform" 
                               value="external" 
                               class="platform-radio"
                               <?php echo e(old('platform', $campaign->platform) == 'external' ? 'checked' : ''); ?>>
                        <label for="external_platform" class="platform-option-label">
                            <div class="platform-card">
                                <div class="platform-icon">
                                    <i class="fas fa-cloud"></i>
                                </div>
                                <h6 class="platform-title">منصة خارجية</h6>
                                <p class="platform-desc">استخدم منصة WhatsApp Business الخاصة بك</p>
                                <div class="platform-features">
                                    <span class="feature-badge">احترافي</span>
                                    <span class="feature-badge">علامتك التجارية</span>
                                    <span class="feature-badge">يتطلب موافقة</span>
                                </div>
                                <div class="platform-pricing">
                                    <span class="price">حسب الاستخدام</span>
                                    <small>أسعارك الخاصة</small>
                                </div>
                            </div>
                        </label>
                    </div>
                </div>
            </div>
        </div>

        <!-- External Platform Configuration -->
        <div id="external-config" class="external-config-section" style="display: none;">
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-cog me-2"></i>
                        إعدادات المنصة الخارجية
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group mb-3">
                                <label for="whatsapp_token" class="form-label">
                                    <i class="fas fa-key me-1"></i>
                                    WhatsApp Business API Token
                                </label>
                                <input type="text" 
                                       id="whatsapp_token" 
                                       name="whatsapp_token" 
                                       class="form-control <?php $__errorArgs = ['whatsapp_token'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                       value="<?php echo e(old('whatsapp_token', $campaign->whatsapp_token)); ?>"
                                       placeholder="أدخل رمز API الخاص بحساب WhatsApp Business">
                                <?php $__errorArgs = ['whatsapp_token'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                <div class="form-text">
                                    <i class="fas fa-info-circle me-1"></i>
                                    يجب أن يكون لديك حساب WhatsApp Business API مُفعّل
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="alert alert-warning">
                        <h6 class="alert-heading">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            تنبيه مهم
                        </h6>
                        <p class="mb-0">استخدام منصة خارجية يتطلب موافقة الإدارة قبل بدء الحملة. قد يستغرق هذا من 24-48 ساعة.</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Messaging Channel Selection -->
        <div class="messaging-channel-section mt-4">
            <h6 class="section-title">
                <i class="fas fa-comments me-2"></i>
                قناة الإرسال
            </h6>
            
            <div class="row">
                <div class="col-md-4">
                    <div class="channel-option">
                        <input type="radio" 
                               id="whatsapp_channel" 
                               name="messaging_channel" 
                               value="whatsapp" 
                               class="channel-radio"
                               <?php echo e(old('messaging_channel', $campaign->messaging_channel ?? 'whatsapp') == 'whatsapp' ? 'checked' : ''); ?>>
                        <label for="whatsapp_channel" class="channel-label">
                            <div class="channel-card">
                                <div class="channel-icon whatsapp">
                                    <i class="fab fa-whatsapp"></i>
                                </div>
                                <h6 class="channel-name">WhatsApp</h6>
                                <p class="channel-desc">رسائل نصية وصور</p>
                                <div class="channel-stats">
                                    <small>معدل الوصول: 98%</small>
                                </div>
                            </div>
                        </label>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="channel-option">
                        <input type="radio" 
                               id="email_channel" 
                               name="messaging_channel" 
                               value="email" 
                               class="channel-radio"
                               <?php echo e(old('messaging_channel', $campaign->messaging_channel) == 'email' ? 'checked' : ''); ?>>
                        <label for="email_channel" class="channel-label">
                            <div class="channel-card">
                                <div class="channel-icon email">
                                    <i class="fas fa-envelope"></i>
                                </div>
                                <h6 class="channel-name">البريد الإلكتروني</h6>
                                <p class="channel-desc">رسائل غنية بالمحتوى</p>
                                <div class="channel-stats">
                                    <small>معدل الوصول: 85%</small>
                                </div>
                            </div>
                        </label>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="channel-option">
                        <input type="radio" 
                               id="sms_channel" 
                               name="messaging_channel" 
                               value="sms" 
                               class="channel-radio"
                               <?php echo e(old('messaging_channel', $campaign->messaging_channel) == 'sms' ? 'checked' : ''); ?>>
                        <label for="sms_channel" class="channel-label">
                            <div class="channel-card">
                                <div class="channel-icon sms">
                                    <i class="fas fa-sms"></i>
                                </div>
                                <h6 class="channel-name">الرسائل النصية</h6>
                                <p class="channel-desc">رسائل نصية مباشرة</p>
                                <div class="channel-stats">
                                    <small>معدل الوصول: 95%</small>
                                </div>
                            </div>
                        </label>
                    </div>
                </div>
            </div>
        </div>

        <!-- Campaign Summary -->
        <div class="campaign-summary mt-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-clipboard-check me-2"></i>
                        ملخص الحملة النهائي
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="summary-section">
                                <h6 class="summary-title">تفاصيل الحملة</h6>
                                <div class="summary-item">
                                    <span class="summary-label">اسم الحملة:</span>
                                    <span class="summary-value"><?php echo e($campaign->campaign_name); ?></span>
                                </div>
                                <div class="summary-item">
                                    <span class="summary-label">نوع الحملة:</span>
                                    <span class="summary-value">
                                        <?php switch($campaign->campaign_type):
                                            case ('promotional'): ?>
                                                ترويجية
                                                <?php break; ?>
                                            <?php case ('seasonal'): ?>
                                                موسمية
                                                <?php break; ?>
                                            <?php case ('loyalty'): ?>
                                                الولاء
                                                <?php break; ?>
                                            <?php case ('birthday'): ?>
                                                عيد ميلاد
                                                <?php break; ?>
                                            <?php case ('appreciation'): ?>
                                                تقدير وشكر
                                                <?php break; ?>
                                        <?php endswitch; ?>
                                    </span>
                                </div>
                                <div class="summary-item">
                                    <span class="summary-label">الهدية:</span>
                                    <span class="summary-value"><?php echo e($campaign->gift->name); ?></span>
                                </div>
                                <div class="summary-item">
                                    <span class="summary-label">عدد الرسائل:</span>
                                    <span class="summary-value"><?php echo e($campaign->message_count ?? 0); ?> رسالة</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="summary-section">
                                <h6 class="summary-title">التكلفة الإجمالية</h6>
                                <div class="cost-summary">
                                    <div class="cost-item">
                                        <span class="cost-label">تكلفة الهدايا:</span>
                                        <span class="cost-value"><?php echo e(number_format(($campaign->gift->price ?? 0) * ($campaign->message_count ?? 0), 2)); ?> ريال</span>
                                    </div>
                                    <div class="cost-item">
                                        <span class="cost-label">تكلفة الرسائل:</span>
                                        <span class="cost-value" id="messaging-cost"><?php echo e(number_format(0.5 * ($campaign->message_count ?? 0), 2)); ?> ريال</span>
                                    </div>
                                    <div class="cost-item total-cost">
                                        <span class="cost-label"><strong>الإجمالي:</strong></span>
                                        <span class="cost-value" id="final-total">
                                            <strong><?php echo e(number_format((($campaign->gift->price ?? 0) * ($campaign->message_count ?? 0)) + (0.5 * ($campaign->message_count ?? 0)), 2)); ?> ريال</strong>
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.platform-selection-stage {
    padding: 1rem 0;
}

.stage-intro {
    text-align: center;
    margin-bottom: 2rem;
}

.stage-title {
    color: #333;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.stage-description {
    color: #6c757d;
    font-size: 1rem;
}

.section-title {
    color: #333;
    font-weight: 600;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
}

.section-title i {
    color: #667eea;
}

.platform-option-card {
    height: 100%;
}

.platform-radio, .channel-radio {
    display: none;
}

.platform-option-label, .channel-label {
    display: block;
    cursor: pointer;
    height: 100%;
}

.platform-card, .channel-card {
    border: 2px solid #e9ecef;
    border-radius: 15px;
    padding: 2rem;
    text-align: center;
    transition: all 0.3s ease;
    height: 100%;
    background: white;
    position: relative;
}

.platform-radio:checked + .platform-option-label .platform-card,
.channel-radio:checked + .channel-label .channel-card {
    border-color: #667eea;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.05) 100%);
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.2);
}

.platform-icon, .channel-icon {
    font-size: 3rem;
    color: #667eea;
    margin-bottom: 1rem;
}

.channel-icon.whatsapp {
    color: #25D366;
}

.channel-icon.email {
    color: #EA4335;
}

.channel-icon.sms {
    color: #1DA1F2;
}

.platform-title, .channel-name {
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #333;
}

.platform-desc, .channel-desc {
    color: #6c757d;
    margin-bottom: 1rem;
    font-size: 0.875rem;
}

.platform-features {
    display: flex;
    gap: 0.5rem;
    justify-content: center;
    flex-wrap: wrap;
    margin-bottom: 1rem;
}

.feature-badge {
    background: #667eea;
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 500;
}

.platform-pricing {
    border-top: 1px solid #e9ecef;
    padding-top: 1rem;
    margin-top: 1rem;
}

.price {
    font-size: 1.25rem;
    font-weight: 700;
    color: #667eea;
    display: block;
}

.channel-stats {
    margin-top: 0.5rem;
    color: #28a745;
    font-weight: 500;
}

.external-config-section .card {
    border: none;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    border-radius: 15px;
}

.external-config-section .card-header {
    background: #f8f9fa;
    border: none;
    border-radius: 15px 15px 0 0;
}

.campaign-summary .card {
    border: none;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    border-radius: 15px;
}

.campaign-summary .card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 15px 15px 0 0;
}

.summary-section {
    padding: 1rem;
}

.summary-title {
    font-weight: 600;
    margin-bottom: 1rem;
    color: #333;
    border-bottom: 2px solid #f8f9fa;
    padding-bottom: 0.5rem;
}

.summary-item, .cost-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.75rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #f8f9fa;
}

.summary-item:last-child, .cost-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.summary-label, .cost-label {
    color: #495057;
    font-weight: 500;
}

.summary-value, .cost-value {
    color: #667eea;
    font-weight: 600;
}

.cost-item.total-cost {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.05) 100%);
    padding: 1rem;
    border-radius: 10px;
    margin-top: 1rem;
    border: none;
}

@media (max-width: 768px) {
    .platform-card, .channel-card {
        padding: 1.5rem;
    }
    
    .platform-icon, .channel-icon {
        font-size: 2rem;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const platformRadios = document.querySelectorAll('input[name="platform"]');
    const externalConfig = document.getElementById('external-config');
    const whatsappTokenInput = document.getElementById('whatsapp_token');
    const messagingCostElement = document.getElementById('messaging-cost');
    const finalTotalElement = document.getElementById('final-total');
    
    const messageCount = <?php echo e($campaign->message_count ?? 0); ?>;
    const giftPrice = <?php echo e($campaign->gift->price ?? 0); ?>;

    function toggleExternalConfig() {
        const selectedPlatform = document.querySelector('input[name="platform"]:checked')?.value;
        
        if (selectedPlatform === 'external') {
            externalConfig.style.display = 'block';
            whatsappTokenInput.required = true;
        } else {
            externalConfig.style.display = 'none';
            whatsappTokenInput.required = false;
        }
        
        updateCosts(selectedPlatform);
    }

    function updateCosts(platform) {
        let messagingCost = 0;
        
        if (platform === 'own') {
            messagingCost = messageCount * 0.5; // 0.5 per message for own platform
        } else {
            messagingCost = 0; // External platform cost is variable
        }
        
        const giftsCost = messageCount * giftPrice;
        const totalCost = giftsCost + messagingCost;
        
        if (platform === 'external') {
            messagingCostElement.textContent = 'حسب أسعارك';
            finalTotalElement.innerHTML = '<strong>' + giftsCost.toLocaleString() + ' + تكلفة المنصة الخارجية</strong>';
        } else {
            messagingCostElement.textContent = messagingCost.toLocaleString() + ' ريال';
            finalTotalElement.innerHTML = '<strong>' + totalCost.toLocaleString() + ' ريال</strong>';
        }
    }

    // Event listeners
    platformRadios.forEach(radio => {
        radio.addEventListener('change', toggleExternalConfig);
    });

    // Initial setup
    toggleExternalConfig();

    // Form validation
    const form = document.querySelector('form');
    if (form) {
        form.addEventListener('submit', function(e) {
            const selectedPlatform = document.querySelector('input[name="platform"]:checked')?.value;
            const selectedChannel = document.querySelector('input[name="messaging_channel"]:checked')?.value;
            
            if (!selectedPlatform) {
                e.preventDefault();
                Swal.fire({
                    title: 'يرجى اختيار المنصة!',
                    text: 'يجب اختيار نوع المنصة للمتابعة',
                    icon: 'warning',
                    confirmButtonText: 'حسناً'
                });
                return;
            }
            
            if (!selectedChannel) {
                e.preventDefault();
                Swal.fire({
                    title: 'يرجى اختيار قناة الإرسال!',
                    text: 'يجب اختيار قناة الإرسال للمتابعة',
                    icon: 'warning',
                    confirmButtonText: 'حسناً'
                });
                return;
            }
            
            if (selectedPlatform === 'external' && !whatsappTokenInput.value.trim()) {
                e.preventDefault();
                whatsappTokenInput.focus();
                Swal.fire({
                    title: 'رمز API مطلوب!',
                    text: 'يرجى إدخال رمز WhatsApp Business API',
                    icon: 'warning',
                    confirmButtonText: 'حسناً'
                });
                return;
            }
        });
    }
});
</script> <?php /**PATH D:\GiftsSaudi-Laravel (1)\resources\views/vendor/campaigns/stages/platform-selection.blade.php ENDPATH**/ ?>