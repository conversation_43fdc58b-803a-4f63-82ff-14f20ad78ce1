

<?php $__env->startSection('page-title', 'الرئيسية'); ?>

<?php $__env->startSection('content'); ?>
<!-- Welcome Section -->
<div class="welcome-section mb-4">
    <div class="stat-card">
        <div class="d-flex align-items-center">
            <div class="welcome-icon me-3">
                <i class="fas fa-smile text-success" style="font-size: 2rem;"></i>
            </div>
            <div>
                <h4 class="mb-1">مرحباً <?php echo e(Auth::guard('employee')->user()->name ?? Auth::user()->name ?? 'الموظف'); ?></h4>
                <p class="text-muted mb-0"><?php echo e(now()->format('l, j F Y')); ?></p>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row">
    <div class="col-6 mb-3">
        <div class="stat-card">
            <div class="stat-number"><?php echo e($stats['deliveries_today']); ?></div>
            <div class="stat-label">تسليمات اليوم</div>
            <div class="stat-icon">
                <i class="fas fa-calendar-day text-success"></i>
            </div>
        </div>
    </div>
    <div class="col-6 mb-3">
        <div class="stat-card warning">
            <div class="stat-number"><?php echo e($stats['pending_deliveries']); ?></div>
            <div class="stat-label">معلقة</div>
            <div class="stat-icon">
                <i class="fas fa-clock text-warning"></i>
            </div>
        </div>
    </div>
    <div class="col-6 mb-3">
        <div class="stat-card info">
            <div class="stat-number"><?php echo e($stats['this_week']); ?></div>
            <div class="stat-label">هذا الأسبوع</div>
            <div class="stat-icon">
                <i class="fas fa-calendar-week text-info"></i>
            </div>
        </div>
    </div>
    <div class="col-6 mb-3">
        <div class="stat-card">
            <div class="stat-number"><?php echo e($stats['total_deliveries']); ?></div>
            <div class="stat-label">إجمالي التسليمات</div>
            <div class="stat-icon">
                <i class="fas fa-trophy text-success"></i>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="quick-actions mb-4">
    <h5 class="mb-3">
        <i class="fas fa-bolt me-2 text-warning"></i>
        إجراءات سريعة
    </h5>
    <div class="row">
        <div class="col-12 mb-2">
            <a href="<?php echo e(route('employee.search')); ?>" class="btn btn-success-mobile">
                <i class="fas fa-search me-2"></i>
                بحث برقم الجوال
            </a>
        </div>
        <div class="col-12 mb-2">
            <a href="<?php echo e(route('employee.statistics')); ?>" class="btn btn-info-mobile">
                <i class="fas fa-chart-bar me-2"></i>
                عرض الإحصائيات التفصيلية
            </a>
        </div>
    </div>
</div>

<!-- Recent Deliveries -->
<?php if($recentDeliveries->count() > 0): ?>
<div class="recent-deliveries">
    <h5 class="mb-3">
        <i class="fas fa-history me-2 text-info"></i>
        آخر التسليمات
    </h5>
    
    <?php $__currentLoopData = $recentDeliveries; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $delivery): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
    <div class="delivery-card">
        <div class="delivery-header">
            <div class="d-flex justify-content-between align-items-start">
                <div>
                    <h6 class="delivery-title"><?php echo e($delivery->client->name); ?></h6>
                    <div class="delivery-info">
                        <small class="text-muted">
                            <i class="fas fa-phone me-1"></i>
                            <?php echo e($delivery->client->phone_number); ?>

                        </small>
                    </div>
                </div>
                <span class="delivery-status status-delivered">
                    مُسلمة
                </span>
            </div>
        </div>
        
        <div class="delivery-content">
            <div class="gift-info">
                <div class="d-flex align-items-center">
                    <i class="fas fa-gift text-success me-2"></i>
                    <span><?php echo e($delivery->gift->name); ?></span>
                </div>
            </div>
            
            <div class="delivery-meta">
                <small class="text-muted">
                    <i class="fas fa-clock me-1"></i>
                    <?php echo e($delivery->delivered_at->diffForHumans()); ?>

                </small>
            </div>
        </div>
    </div>
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
</div>
<?php else: ?>
<div class="no-deliveries">
    <div class="text-center py-4">
        <div class="empty-icon mb-3">
            <i class="fas fa-box-open text-muted" style="font-size: 3rem;"></i>
        </div>
        <h6 class="text-muted">لا توجد تسليمات حديثة</h6>
        <p class="text-muted small">ابدأ بالبحث عن العملاء لتسليم الهدايا</p>
        <a href="<?php echo e(route('employee.search')); ?>" class="btn btn-success-mobile mt-2">
            <i class="fas fa-search me-2"></i>
            ابدأ البحث
        </a>
    </div>
</div>
<?php endif; ?>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<style>
.welcome-section .stat-card {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    border: none;
}

.welcome-section .stat-card h4 {
    color: white;
    margin-bottom: 0.5rem;
}

.stat-card {
    position: relative;
    overflow: hidden;
}

.stat-icon {
    position: absolute;
    top: 1rem;
    left: 1rem;
    font-size: 1.5rem;
    opacity: 0.3;
}

.quick-actions .btn {
    text-align: right;
    justify-content: flex-start;
}

.delivery-card {
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
}

.delivery-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
}

.delivery-info {
    margin-top: 0.25rem;
}

.gift-info {
    background: rgba(40, 167, 69, 0.1);
    padding: 0.5rem;
    border-radius: 8px;
    margin-bottom: 0.5rem;
}

.delivery-meta {
    border-top: 1px solid #f8f9fa;
    padding-top: 0.5rem;
    margin-top: 0.5rem;
}

.empty-icon {
    opacity: 0.5;
}

@media (max-width: 576px) {
    .stat-card {
        padding: 1rem;
    }
    
    .stat-number {
        font-size: 1.5rem;
    }
    
    .stat-label {
        font-size: 0.8rem;
    }
}
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add some animation to stats cards
    const statCards = document.querySelectorAll('.stat-card');
    
    statCards.forEach((card, index) => {
        setTimeout(() => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(20px)';
            card.style.transition = 'all 0.5s ease';
            
            setTimeout(() => {
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }, 100);
        }, index * 100);
    });
});
</script>
<?php $__env->stopPush(); ?> 
<?php echo $__env->make('layouts.employee', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\GiftsSaudi-Laravel (1)\resources\views/employee/dashboard.blade.php ENDPATH**/ ?>