

<?php $__env->startSection('title', 'تفاصيل العميل'); ?>
<?php $__env->startSection('page-title', 'تفاصيل العميل'); ?>

<?php $__env->startSection('content'); ?>
<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0"><?php echo e($client->name); ?></h5>
            </div>
            <div class="card-body">
                <p><strong>الإيميل:</strong> <?php echo e($client->email ?? 'غير محدد'); ?></p>
                <p><strong>الهاتف:</strong> <?php echo e($client->phone ?? 'غير محدد'); ?></p>
                <p><strong>المدينة:</strong> <?php echo e($client->city ?? 'غير محدد'); ?></p>
                <p><strong>تاريخ التسجيل:</strong> <?php echo e($client->created_at ? $client->created_at->format('Y/m/d') : 'غير محدد'); ?></p>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">الإجراءات</h5>
            </div>
            <div class="card-body d-grid gap-2">
                <a href="<?php echo e(route('admin.clients.edit', $client)); ?>" class="btn btn-primary">
                    <i class="fas fa-edit"></i> تعديل
                </a>
                <a href="<?php echo e(route('admin.clients.index')); ?>" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-right"></i> العودة
                </a>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?> 
<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\GiftsSaudi-Laravel (1)\resources\views\admin\clients\show.blade.php ENDPATH**/ ?>