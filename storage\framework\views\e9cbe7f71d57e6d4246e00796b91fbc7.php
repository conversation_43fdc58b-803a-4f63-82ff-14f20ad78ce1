

<?php $__env->startSection('title', 'إدارة الهدايا'); ?>
<?php $__env->startSection('page-title', 'إدارة الهدايا'); ?>

<?php $__env->startPush('styles'); ?>
<style>
    /* Professional Gifts Management Styles */
    .gifts-container {
        background: transparent;
    }

    /* Page Header */
    .page-header {
        background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
        border: 1px solid rgba(255, 107, 53, 0.2);
        border-radius: 25px;
        padding: 30px;
        margin-bottom: 30px;
        position: relative;
        overflow: hidden;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4);
    }

    .page-header::before {
        content: '';
        position: absolute;
        top: -50%;
        right: -50%;
        width: 200%;
        height: 200%;
        background: linear-gradient(45deg, transparent, rgba(255, 107, 53, 0.1), transparent);
        transform: rotate(45deg);
        animation: headerShimmer 4s ease-in-out infinite;
    }

    @keyframes headerShimmer {
        0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
        100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
    }

    .page-title {
        color: white;
        font-weight: 800;
        font-size: 2.2rem;
        margin-bottom: 10px;
        background: linear-gradient(135deg, #ffffff 0%, #ff6b35 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        position: relative;
        z-index: 2;
    }

    .page-subtitle {
        color: #b0b0b0;
        font-size: 1rem;
        position: relative;
        z-index: 2;
    }

    /* Statistics Cards */
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 25px;
        margin-bottom: 40px;
    }

    .stat-card {
        background: linear-gradient(145deg, #2d2d2d, #1a1a1a);
        border: 1px solid rgba(255, 107, 53, 0.1);
        border-radius: 20px;
        padding: 30px;
        position: relative;
        overflow: hidden;
        transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
    }

    .stat-card:hover {
        transform: translateY(-8px) scale(1.02);
        box-shadow: 0 25px 50px rgba(0, 0, 0, 0.4);
        border-color: rgba(255, 107, 53, 0.3);
    }

    .stat-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: var(--card-gradient);
        border-radius: 20px 20px 0 0;
    }

    .stat-card.primary::before { background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%); }
    .stat-card.success::before { background: linear-gradient(135deg, #00d4aa 0%, #00bf95 100%); }
    .stat-card.info::before { background: linear-gradient(135deg, #3867d6 0%, #8e44ad 100%); }
    .stat-card.warning::before { background: linear-gradient(135deg, #ffc107 0%, #ff8f00 100%); }

    .stat-content {
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .stat-info h4 {
        font-size: 2.2rem;
        font-weight: 800;
        color: white;
        margin-bottom: 5px;
    }

    .stat-info p {
        color: #b0b0b0;
        font-size: 0.9rem;
        font-weight: 600;
        margin: 0;
    }

    .stat-icon {
        width: 60px;
        height: 60px;
        border-radius: 15px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.8rem;
        color: white;
        background: var(--card-gradient);
        box-shadow: 0 10px 25px rgba(255, 107, 53, 0.3);
    }

    .stat-card.primary .stat-icon { 
        background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
        box-shadow: 0 10px 25px rgba(255, 107, 53, 0.3);
    }
    .stat-card.success .stat-icon { 
        background: linear-gradient(135deg, #00d4aa 0%, #00bf95 100%);
        box-shadow: 0 10px 25px rgba(0, 212, 170, 0.3);
    }
    .stat-card.info .stat-icon { 
        background: linear-gradient(135deg, #3867d6 0%, #8e44ad 100%);
        box-shadow: 0 10px 25px rgba(56, 103, 214, 0.3);
    }
    .stat-card.warning .stat-icon { 
        background: linear-gradient(135deg, #ffc107 0%, #ff8f00 100%);
        box-shadow: 0 10px 25px rgba(255, 193, 7, 0.3);
    }

    /* Filters Card */
    .filter-card {
        background: linear-gradient(145deg, #2d2d2d, #1a1a1a);
        border: 1px solid rgba(255, 107, 53, 0.1);
        border-radius: 20px;
        padding: 25px;
        margin-bottom: 30px;
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
    }

    .filter-card .form-label {
        color: #e0e0e0;
        font-weight: 600;
        margin-bottom: 8px;
    }

    .filter-card .form-control,
    .filter-card .form-select {
        background: rgba(255, 255, 255, 0.05);
        border: 1px solid rgba(255, 107, 53, 0.2);
        border-radius: 12px;
        color: white;
        padding: 12px 15px;
    }

    .filter-card .form-control:focus,
    .filter-card .form-select:focus {
        background: rgba(255, 255, 255, 0.08);
        border-color: var(--accent-color);
        box-shadow: 0 0 0 0.2rem rgba(255, 107, 53, 0.25);
        color: white;
    }

    .filter-card .form-control::placeholder {
        color: #888;
    }

    .filter-card .btn-primary {
        background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
        border: none;
        border-radius: 12px;
        padding: 12px 20px;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .filter-card .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(255, 107, 53, 0.4);
    }

    /* Gifts Table */
    .gifts-table-card {
        background: linear-gradient(145deg, #2d2d2d, #1a1a1a);
        border: 1px solid rgba(255, 107, 53, 0.1);
        border-radius: 20px;
        overflow: hidden;
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
    }

    .gifts-table-card .table {
        margin: 0;
        color: #e0e0e0;
    }

    .gifts-table-card .table thead th {
        background: linear-gradient(135deg, #1a1a1a 0%, #0f0f0f 100%);
        border: none;
        color: white !important;
        font-weight: 700;
        padding: 20px 15px;
        font-size: 0.9rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .gifts-table-card .table tbody tr {
        border-bottom: 1px solid rgba(255, 107, 53, 0.1);
        transition: all 0.3s ease;
    }

    .gifts-table-card .table tbody tr:hover {
        background: rgba(255, 107, 53, 0.05);
    }

    .gifts-table-card .table tbody td {
        border: none;
        padding: 20px 15px;
        vertical-align: middle;
    }

    /* Gift Image */
    .gift-image {
        width: 60px;
        height: 60px;
        border-radius: 12px;
        object-fit: cover;
        background: linear-gradient(135deg, #2d2d2d, #1a1a1a);
        border: 1px solid rgba(255, 107, 53, 0.2);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        color: #ff6b35;
        margin-left: 10px;
    }

    /* Status Badges */
    .status-badge {
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 0.75rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .status-available { 
        background: linear-gradient(135deg, rgba(0, 212, 170, 0.2), rgba(0, 191, 149, 0.2)); 
        color: #00d4aa; 
        border: 1px solid rgba(0, 212, 170, 0.3);
    }
    .status-out_of_stock { 
        background: linear-gradient(135deg, rgba(255, 71, 87, 0.2), rgba(196, 69, 105, 0.2)); 
        color: #ff4757; 
        border: 1px solid rgba(255, 71, 87, 0.3);
    }
    .status-discontinued { 
        background: linear-gradient(135deg, rgba(108, 117, 125, 0.2), rgba(73, 80, 87, 0.2)); 
        color: #6c757d; 
        border: 1px solid rgba(108, 117, 125, 0.3);
    }

    .approval-approved { 
        background: linear-gradient(135deg, rgba(0, 212, 170, 0.2), rgba(0, 191, 149, 0.2)); 
        color: #00d4aa; 
        border: 1px solid rgba(0, 212, 170, 0.3);
    }
    .approval-pending { 
        background: linear-gradient(135deg, rgba(255, 193, 7, 0.2), rgba(255, 143, 0, 0.2)); 
        color: #ffc107; 
        border: 1px solid rgba(255, 193, 7, 0.3);
    }

    /* Gift Price */
    .gift-price {
        font-size: 1.1rem;
        font-weight: 700;
        color: #00d4aa;
        margin-bottom: 3px;
    }

    .gift-details {
        font-size: 0.85rem;
        color: #b0b0b0;
        line-height: 1.4;
    }

    .gift-name {
        color: white;
        font-weight: 600;
        font-size: 0.95rem;
        margin-bottom: 3px;
    }

    .gift-vendor {
        color: #888;
        font-size: 0.8rem;
    }

    /* Action Buttons */
    .action-buttons .btn {
        margin: 0 2px;
        padding: 8px 12px;
        font-size: 0.8rem;
        border-radius: 8px;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .action-buttons .btn-outline-primary {
        border-color: rgba(255, 107, 53, 0.5);
        color: #ff6b35;
    }

    .action-buttons .btn-outline-primary:hover {
        background: #ff6b35;
        border-color: #ff6b35;
        color: white;
        transform: translateY(-2px);
    }

    .action-buttons .btn-outline-success {
        border-color: rgba(0, 212, 170, 0.5);
        color: #00d4aa;
    }

    .action-buttons .btn-outline-success:hover {
        background: #00d4aa;
        border-color: #00d4aa;
        color: white;
        transform: translateY(-2px);
    }

    .action-buttons .btn-outline-warning {
        border-color: rgba(255, 193, 7, 0.5);
        color: #ffc107;
    }

    .action-buttons .btn-outline-warning:hover {
        background: #ffc107;
        border-color: #ffc107;
        color: white;
        transform: translateY(-2px);
    }

    .action-buttons .btn-outline-danger {
        border-color: rgba(255, 71, 87, 0.5);
        color: #ff4757;
    }

    .action-buttons .btn-outline-danger:hover {
        background: #ff4757;
        border-color: #ff4757;
        color: white;
        transform: translateY(-2px);
    }

    /* Add Gift Button */
    .add-gift-btn {
        background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
        border: none;
        border-radius: 15px;
        padding: 12px 25px;
        font-weight: 700;
        color: white;
        text-decoration: none;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        gap: 8px;
    }

    .add-gift-btn:hover {
        color: white;
        transform: translateY(-3px);
        box-shadow: 0 10px 30px rgba(255, 107, 53, 0.4);
    }

    /* Bulk Actions */
    .bulk-actions {
        background: linear-gradient(145deg, #2d2d2d, #1a1a1a);
        border: 1px solid rgba(255, 107, 53, 0.1);
        border-radius: 15px;
        padding: 15px 20px;
        margin-bottom: 20px;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
        display: none;
        align-items: center;
        gap: 15px;
    }

    .bulk-actions.show {
        display: flex;
    }

    .bulk-actions .btn {
        border-radius: 10px;
        padding: 8px 15px;
        font-weight: 600;
        font-size: 0.85rem;
        transition: all 0.3s ease;
    }

    .bulk-actions .btn:hover {
        transform: translateY(-2px);
    }

    /* Text colors for consistency */
    .text-secondary,
    .text-body-secondary,
    small,
    .form-text {
        color: #b0b0b0 !important;
    }

    /* Pagination */
    .pagination {
        justify-content: center;
        margin-top: 30px;
    }

    .pagination .page-link {
        background: rgba(255, 255, 255, 0.05);
        border: 1px solid rgba(255, 107, 53, 0.2);
        color: #b0b0b0;
        margin: 0 2px;
        border-radius: 8px;
        padding: 10px 15px;
        transition: all 0.3s ease;
    }

    .pagination .page-link:hover {
        background: rgba(255, 107, 53, 0.1);
        border-color: var(--accent-color);
        color: white;
        transform: translateY(-2px);
    }

    .pagination .page-item.active .page-link {
        background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
        border-color: var(--accent-color);
        color: white;
    }
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
<div class="gifts-container">
    <!-- Page Header -->
    <div class="page-header">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="page-title">إدارة الهدايا</h1>
                <p class="page-subtitle">تتبع وإدارة كتالوج الهدايا المتاحة</p>
            </div>
            <a href="<?php echo e(route('admin.gifts.create')); ?>" class="add-gift-btn">
                <i class="fas fa-plus"></i>
                إضافة هدية جديدة
            </a>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="stats-grid">
        <div class="stat-card primary">
            <div class="stat-content">
                <div class="stat-info">
                    <h4><?php echo e($stats['total']); ?></h4>
                    <p>إجمالي الهدايا</p>
                </div>
                <div class="stat-icon">
                    <i class="fas fa-gifts"></i>
                </div>
            </div>
        </div>
        <div class="stat-card success">
            <div class="stat-content">
                <div class="stat-info">
                    <h4><?php echo e($stats['approved']); ?></h4>
                    <p>الهدايا المعتمدة</p>
                </div>
                <div class="stat-icon">
                    <i class="fas fa-check"></i>
                </div>
            </div>
        </div>
        <div class="stat-card warning">
            <div class="stat-content">
                <div class="stat-info">
                    <h4><?php echo e($stats['pending']); ?></h4>
                    <p>في الانتظار</p>
                </div>
                <div class="stat-icon">
                    <i class="fas fa-clock"></i>
                </div>
            </div>
        </div>
        <div class="stat-card info">
            <div class="stat-content">
                <div class="stat-info">
                    <h4><?php echo e($stats['available']); ?></h4>
                    <p>متاحة للطلب</p>
                </div>
                <div class="stat-icon">
                    <i class="fas fa-check-circle"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="filter-card">
        <form method="GET" action="<?php echo e(route('admin.gifts.index')); ?>" class="row g-3">
            <div class="col-md-3">
                <label class="form-label">البحث</label>
                <input type="text" name="search" class="form-control" placeholder="اسم الهدية أو الوصف..." 
                       value="<?php echo e(request('search')); ?>">
            </div>
            
            <div class="col-md-2">
                <label class="form-label">التاجر</label>
                <select name="vendor_id" class="form-select">
                    <option value="">جميع التجار</option>
                    <?php $__currentLoopData = $vendors; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $vendor): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <option value="<?php echo e($vendor->id); ?>" <?php echo e(request('vendor_id') == $vendor->id ? 'selected' : ''); ?>>
                            <?php echo e($vendor->company_name ?? $vendor->user->name); ?>

                        </option>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </select>
            </div>
            
            <div class="col-md-2">
                <label class="form-label">الفئة</label>
                <select name="category" class="form-select">
                    <option value="">جميع الفئات</option>
                    <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <option value="<?php echo e($category); ?>" <?php echo e(request('category') == $category ? 'selected' : ''); ?>>
                            <?php echo e($category); ?>

                        </option>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </select>
            </div>
            
            <div class="col-md-2">
                <label class="form-label">الحالة</label>
                <select name="status" class="form-select">
                    <option value="">جميع الحالات</option>
                    <option value="available" <?php echo e(request('status') == 'available' ? 'selected' : ''); ?>>متاحة</option>
                    <option value="out_of_stock" <?php echo e(request('status') == 'out_of_stock' ? 'selected' : ''); ?>>نفدت</option>
                    <option value="discontinued" <?php echo e(request('status') == 'discontinued' ? 'selected' : ''); ?>>متوقفة</option>
                </select>
            </div>
            
            <div class="col-md-2">
                <label class="form-label">الاعتماد</label>
                <select name="approved" class="form-select">
                    <option value="">الكل</option>
                    <option value="1" <?php echo e(request('approved') == '1' ? 'selected' : ''); ?>>معتمد</option>
                    <option value="0" <?php echo e(request('approved') == '0' ? 'selected' : ''); ?>>في الانتظار</option>
                </select>
            </div>
            
            <div class="col-md-1">
                <label class="form-label">&nbsp;</label>
                <button type="submit" class="btn btn-primary w-100">
                    <i class="fas fa-search"></i>
                </button>
            </div>
        </form>
    </div>

    <!-- Bulk Actions -->
    <div class="bulk-actions" id="bulkActions">
        <div class="d-flex align-items-center gap-3">
            <span class="fw-bold text-white">الإجراءات المحددة:</span>
            <button type="button" class="btn btn-success btn-sm" onclick="bulkAction('approve')">
                <i class="fas fa-check me-1"></i> اعتماد
            </button>
            <button type="button" class="btn btn-warning btn-sm" onclick="bulkAction('reject')">
                <i class="fas fa-times me-1"></i> رفض
            </button>
            <button type="button" class="btn btn-danger btn-sm" onclick="bulkAction('delete')">
                <i class="fas fa-trash me-1"></i> حذف
            </button>
        </div>
    </div>

    <!-- Gifts Table -->
    <div class="gifts-table-card">
        <div class="table-responsive">
            <table class="table">
                <thead>
                    <tr>
                        <th>
                            <input type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                        </th>
                        <th>الهدية</th>
                        <th>التاجر</th>
                        <th>الفئة</th>
                        <th>السعر</th>
                        <th>المخزون</th>
                        <th>الحالة</th>
                        <th>الاعتماد</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    <?php $__empty_1 = true; $__currentLoopData = $gifts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $gift): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <tr>
                            <td>
                                <input type="checkbox" class="gift-checkbox" value="<?php echo e($gift->id); ?>" onchange="updateBulkActions()">
                            </td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="gift-image">
                                        <?php if($gift->image): ?>
                                            <img src="<?php echo e(asset('storage/' . $gift->image)); ?>" alt="<?php echo e($gift->name); ?>" style="width: 100%; height: 100%; object-fit: cover; border-radius: 12px;">
                                        <?php else: ?>
                                            <i class="fas fa-gift"></i>
                                        <?php endif; ?>
                                    </div>
                                    <div>
                                        <div class="gift-name"><?php echo e($gift->name); ?></div>
                                        <div class="gift-vendor"><?php echo e(Str::limit($gift->description, 50)); ?></div>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div class="gift-details">
                                    <div class="gift-vendor"><?php echo e($gift->vendor->company_name ?? $gift->vendor->user->name); ?></div>
                                </div>
                            </td>
                            <td>
                                <span class="status-badge" style="background: rgba(255, 107, 53, 0.1); color: #ff6b35; border: 1px solid rgba(255, 107, 53, 0.3);">
                                    <?php echo e($gift->category ?? 'غير محدد'); ?>

                                </span>
                            </td>
                            <td>
                                <div class="gift-price"><?php echo e(number_format($gift->price, 2)); ?> ريال</div>
                            </td>
                            <td>
                                <div class="text-white font-weight-bold"><?php echo e($gift->stock_quantity ?? 'غير محدود'); ?></div>
                            </td>
                            <td>
                                <span class="status-badge status-<?php echo e($gift->status); ?>">
                                    <?php if($gift->status == 'available'): ?> متاحة
                                    <?php elseif($gift->status == 'out_of_stock'): ?> نفدت
                                    <?php else: ?> متوقفة
                                    <?php endif; ?>
                                </span>
                            </td>
                            <td>
                                <span class="status-badge approval-<?php echo e($gift->approved ? 'approved' : 'pending'); ?>">
                                    <?php echo e($gift->approved ? 'معتمد' : 'في الانتظار'); ?>

                                </span>
                            </td>
                            <td>
                                <div class="action-buttons">
                                    <a href="<?php echo e(route('admin.gifts.show', $gift)); ?>" class="btn btn-outline-primary btn-sm" title="عرض">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="<?php echo e(route('admin.gifts.edit', $gift)); ?>" class="btn btn-outline-success btn-sm" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <?php if(!$gift->approved): ?>
                                        <form method="POST" action="<?php echo e(route('admin.gifts.approve', $gift)); ?>" class="d-inline">
                                            <?php echo csrf_field(); ?>
                                            <?php echo method_field('PATCH'); ?>
                                            <button type="submit" class="btn btn-outline-warning btn-sm" title="اعتماد">
                                                <i class="fas fa-check"></i>
                                            </button>
                                        </form>
                                    <?php endif; ?>
                                    <form method="POST" action="<?php echo e(route('admin.gifts.destroy', $gift)); ?>" class="d-inline">
                                        <?php echo csrf_field(); ?>
                                        <?php echo method_field('DELETE'); ?>
                                        <button type="submit" class="btn btn-outline-danger btn-sm" 
                                                onclick="return confirm('هل أنت متأكد من حذف هذه الهدية؟')" title="حذف">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <tr>
                            <td colspan="9" class="text-center text-secondary py-5">
                                <i class="fas fa-gifts fa-3x mb-3 text-orange"></i>
                                <div>لا توجد هدايا مطابقة للمعايير المحددة</div>
                            </td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>

    <!-- Pagination -->
    <?php if(method_exists($gifts, 'links')): ?>
        <div class="d-flex justify-content-center">
            <?php echo e($gifts->links()); ?>

        </div>
    <?php endif; ?>
</div>

<script>
// Bulk actions functionality
function toggleSelectAll() {
    const selectAll = document.getElementById('selectAll');
    const checkboxes = document.querySelectorAll('.gift-checkbox');
    
    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAll.checked;
    });
    
    updateBulkActions();
}

function updateBulkActions() {
    const checkedBoxes = document.querySelectorAll('.gift-checkbox:checked');
    const bulkActions = document.getElementById('bulkActions');
    
    if (checkedBoxes.length > 0) {
        bulkActions.classList.add('show');
    } else {
        bulkActions.classList.remove('show');
    }
}

function bulkAction(action) {
    const checkedBoxes = document.querySelectorAll('.gift-checkbox:checked');
    const ids = Array.from(checkedBoxes).map(cb => cb.value);
    
    if (ids.length === 0) {
        alert('يرجى اختيار هدية واحدة على الأقل');
        return;
    }
    
    let confirmMessage = '';
    switch(action) {
        case 'approve':
            confirmMessage = 'هل أنت متأكد من اعتماد الهدايا المحددة؟';
            break;
        case 'reject':
            confirmMessage = 'هل أنت متأكد من رفض الهدايا المحددة؟';
            break;
        case 'delete':
            confirmMessage = 'هل أنت متأكد من حذف الهدايا المحددة؟ هذا الإجراء لا يمكن التراجع عنه.';
            break;
    }
    
    if (confirm(confirmMessage)) {
        // Create form and submit
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '<?php echo e(route("admin.gifts.bulk-action")); ?>';
        
        form.innerHTML = `
            <?php echo csrf_field(); ?>
            <input type="hidden" name="action" value="${action}">
            <input type="hidden" name="ids" value="${ids.join(',')}">
        `;
        
        document.body.appendChild(form);
        form.submit();
    }
}
</script>
<?php $__env->stopSection(); ?> 
<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\GiftsSaudi-Laravel (1)\resources\views/admin/gifts/index.blade.php ENDPATH**/ ?>