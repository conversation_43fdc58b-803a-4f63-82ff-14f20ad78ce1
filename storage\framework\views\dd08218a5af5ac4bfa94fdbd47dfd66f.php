

<?php $__env->startSection('title', 'معالج إنشاء الحملة - ' . $campaign->campaign_name); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-header">
                <h1 class="page-title">
                    <i class="fas fa-magic me-2"></i>
                    معالج إنشاء الحملة الإعلانية
                </h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="<?php echo e(route('vendor.dashboard')); ?>">لوحة التحكم</a></li>
                        <li class="breadcrumb-item"><a href="<?php echo e(route('vendor.campaigns.index')); ?>">الحملات الإعلانية</a></li>
                        <li class="breadcrumb-item active" aria-current="page"><?php echo e($campaign->campaign_name); ?></li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <!-- Campaign Progress -->
            <div class="campaign-progress-card mb-4">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title mb-3">
                            <i class="fas fa-tasks me-2"></i>
                            مراحل إنشاء الحملة
                        </h5>
                        
                        <div class="progress-wizard">
                            <?php
                                $stages = [
                                    'template_selection' => ['title' => 'اختيار القالب', 'icon' => 'fas fa-palette'],
                                    'client_filtering' => ['title' => 'فلترة العملاء', 'icon' => 'fas fa-filter'],
                                    'message_count' => ['title' => 'عدد الرسائل', 'icon' => 'fas fa-calculator'],
                                    'platform_selection' => ['title' => 'منصة الإرسال', 'icon' => 'fas fa-paper-plane'],
                                    'pending_approval' => ['title' => 'انتظار الموافقة', 'icon' => 'fas fa-hourglass-half']
                                ];
                                $currentStageIndex = array_search($campaign->stage, array_keys($stages));
                            ?>
                            
                            <div class="progress-steps">
                                <?php $__currentLoopData = $stages; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $stageKey => $stageInfo): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <?php
                                        $stageIndex = array_search($stageKey, array_keys($stages));
                                        $isActive = $campaign->stage === $stageKey;
                                        $isCompleted = $stageIndex < $currentStageIndex;
                                        $isAccessible = $stageIndex <= $currentStageIndex;
                                    ?>
                                    
                                    <div class="progress-step <?php echo e($isActive ? 'active' : ''); ?> <?php echo e($isCompleted ? 'completed' : ''); ?>">
                                        <div class="step-icon">
                                            <i class="<?php echo e($stageInfo['icon']); ?>"></i>
                                        </div>
                                        <div class="step-title"><?php echo e($stageInfo['title']); ?></div>
                                    </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Campaign Information -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="card campaign-info-card">
                        <div class="card-body">
                            <h6 class="card-title">
                                <i class="fas fa-info-circle me-2"></i>
                                معلومات الحملة
                            </h6>
                            <div class="info-item">
                                <span class="label">اسم الحملة:</span>
                                <span class="value"><?php echo e($campaign->campaign_name); ?></span>
                            </div>
                            <div class="info-item">
                                <span class="label">نوع الحملة:</span>
                                <span class="value">
                                    <?php switch($campaign->campaign_type):
                                        case ('promotional'): ?>
                                            <i class="fas fa-percent me-1"></i>ترويجية
                                            <?php break; ?>
                                        <?php case ('seasonal'): ?>
                                            <i class="fas fa-calendar-alt me-1"></i>موسمية
                                            <?php break; ?>
                                        <?php case ('loyalty'): ?>
                                            <i class="fas fa-heart me-1"></i>الولاء
                                            <?php break; ?>
                                        <?php case ('birthday'): ?>
                                            <i class="fas fa-birthday-cake me-1"></i>عيد ميلاد
                                            <?php break; ?>
                                        <?php case ('appreciation'): ?>
                                            <i class="fas fa-thumbs-up me-1"></i>تقدير وشكر
                                            <?php break; ?>
                                    <?php endswitch; ?>
                                </span>
                            </div>
                            <div class="info-item">
                                <span class="label">الهدية:</span>
                                <span class="value"><?php echo e($campaign->gift->name); ?></span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card campaign-stats-card">
                        <div class="card-body">
                            <h6 class="card-title">
                                <i class="fas fa-chart-bar me-2"></i>
                                إحصائيات الحملة
                            </h6>
                            <div class="stats-grid">
                                <div class="stat-item">
                                    <div class="stat-value"><?php echo e($campaign->target_client_count ?? 0); ?></div>
                                    <div class="stat-label">العملاء المستهدفين</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-value"><?php echo e($campaign->message_count ?? 0); ?></div>
                                    <div class="stat-label">عدد الرسائل</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-value"><?php echo e(number_format($campaign->total_cost ?? 0, 2)); ?></div>
                                    <div class="stat-label">التكلفة الإجمالية</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Current Stage Form -->
            <div class="card stage-form-card">
                <div class="card-header">
                    <h4 class="card-title mb-0">
                        <?php switch($campaign->stage):
                            case ('template_selection'): ?>
                                <i class="fas fa-palette me-2"></i>اختيار قالب الرسالة
                                <?php break; ?>
                            <?php case ('client_filtering'): ?>
                                <i class="fas fa-filter me-2"></i>فلترة العملاء المستهدفين
                                <?php break; ?>
                            <?php case ('message_count'): ?>
                                <i class="fas fa-calculator me-2"></i>تحديد عدد الرسائل
                                <?php break; ?>
                            <?php case ('platform_selection'): ?>
                                <i class="fas fa-paper-plane me-2"></i>اختيار منصة الإرسال
                                <?php break; ?>
                            <?php case ('pending_approval'): ?>
                                <i class="fas fa-hourglass-half me-2"></i>في انتظار موافقة الإدارة
                                <?php break; ?>
                        <?php endswitch; ?>
                    </h4>
                </div>

                <div class="card-body">
                    <?php if($campaign->stage !== 'pending_approval'): ?>
                        <form action="<?php echo e(route('vendor.campaigns.update-stage', $campaign)); ?>" method="POST" enctype="multipart/form-data">
                            <?php echo csrf_field(); ?>
                            <?php echo method_field('POST'); ?>

                            <?php switch($campaign->stage):
                                case ('template_selection'): ?>
                                    <?php echo $__env->make('vendor.campaigns.stages.template-selection', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                                    <?php break; ?>
                                <?php case ('client_filtering'): ?>
                                    <?php echo $__env->make('vendor.campaigns.stages.client-filtering', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                                    <?php break; ?>
                                <?php case ('message_count'): ?>
                                    <?php echo $__env->make('vendor.campaigns.stages.message-count', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                                    <?php break; ?>
                                <?php case ('platform_selection'): ?>
                                    <?php echo $__env->make('vendor.campaigns.stages.platform-selection', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                                    <?php break; ?>
                            <?php endswitch; ?>

                            <div class="form-actions mt-4">
                                <div class="d-flex justify-content-between">
                                    <?php if($campaign->stage !== 'template_selection'): ?>
                                        <form action="<?php echo e(route('vendor.campaigns.go-back-stage', $campaign)); ?>" method="POST" style="display: inline;">
                                            <?php echo csrf_field(); ?>
                                            <button type="submit" class="btn btn-outline-secondary">
                                                <i class="fas fa-arrow-left me-2"></i>
                                                المرحلة السابقة
                                            </button>
                                        </form>
                                    <?php else: ?>
                                        <a href="<?php echo e(route('vendor.campaigns.index')); ?>" class="btn btn-outline-secondary">
                                            <i class="fas fa-times me-2"></i>
                                            إلغاء
                                        </a>
                                    <?php endif; ?>

                                    <button type="submit" class="btn btn-primary btn-lg">
                                        <?php if($campaign->stage === 'platform_selection'): ?>
                                            <i class="fas fa-check me-2"></i>
                                            إرسال للموافقة
                                        <?php else: ?>
                                            <i class="fas fa-arrow-right me-2"></i>
                                            المرحلة التالية
                                        <?php endif; ?>
                                    </button>
                                </div>
                            </div>
                        </form>
                    <?php else: ?>
                        <div class="alert alert-info text-center">
                            <h5><i class="fas fa-hourglass-half me-2"></i>الحملة في انتظار موافقة الإدارة</h5>
                            <p class="mb-0">تم إرسال الحملة للإدارة للمراجعة والموافقة. سيتم إشعارك عند الموافقة عليها.</p>
                            <div class="mt-3">
                                <a href="<?php echo e(route('vendor.campaigns.index')); ?>" class="btn btn-primary">
                                    <i class="fas fa-list me-2"></i>
                                    العودة للحملات
                                </a>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.campaign-progress-card .card {
    border: none;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    border-radius: 15px;
}

.progress-wizard {
    padding: 1rem 0;
}

.progress-steps {
    display: flex;
    justify-content: space-between;
    position: relative;
}

.progress-steps::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 2px;
    background: #e9ecef;
    z-index: 1;
    transform: translateY(-50%);
}

.progress-step {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    z-index: 2;
}

.step-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: #e9ecef;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 0.5rem;
    transition: all 0.3s ease;
}

.progress-step.completed .step-icon {
    background: #28a745;
    color: white;
}

.progress-step.active .step-icon {
    background: #667eea;
    color: white;
    transform: scale(1.1);
}

.step-title {
    font-size: 0.875rem;
    text-align: center;
    font-weight: 500;
    color: #6c757d;
    max-width: 100px;
}

.progress-step.active .step-title {
    color: #667eea;
    font-weight: 600;
}

.campaign-info-card, .campaign-stats-card {
    border: none;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    border-radius: 15px;
    height: 100%;
}

.info-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.75rem;
    padding-bottom: 0.75rem;
    border-bottom: 1px solid #f8f9fa;
}

.info-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

.info-item .label {
    font-weight: 600;
    color: #495057;
}

.info-item .value {
    color: #667eea;
    font-weight: 500;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
}

.stat-item {
    text-align: center;
}

.stat-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: #667eea;
}

.stat-label {
    font-size: 0.875rem;
    color: #6c757d;
    margin-top: 0.25rem;
}

.stage-form-card {
    border: none;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    border-radius: 15px;
}

.stage-form-card .card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 15px 15px 0 0;
}

.form-actions {
    padding-top: 2rem;
    border-top: 2px solid #e9ecef;
}

@media (max-width: 768px) {
    .progress-steps {
        flex-direction: column;
        gap: 1rem;
    }
    
    .progress-steps::before {
        display: none;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
}
</style>

<?php $__env->stopSection(); ?> 
<?php echo $__env->make('layouts.vendor', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\GiftsSaudi-Laravel (1)\resources\views/vendor/campaigns/wizard.blade.php ENDPATH**/ ?>