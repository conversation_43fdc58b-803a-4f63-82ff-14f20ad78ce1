<div class="form-section">
    <h3 class="section-title text-white">
        <i class="fas fa-calculator stage-icon"></i>
        تحديد عدد الرسائل
    </h3>
    
    <div class="row">
        <div class="col-md-12">
            <label for="message_count" class="form-label text-white">عدد الرسائل المراد إرسالها</label>
            <input type="number" name="message_count" id="message_count"
                   class="form-control"
                   value="<?php echo e($campaign->message_count ?? 1); ?>"
                   min="1"
                   max="1000"
                   placeholder="أدخل عدد الرسائل"
                   required>
            <small class="text-muted">يمكنك إرسال رسائل أكثر من عدد العملاء المستهدفين (الحد الأقصى: 1000)</small>
        </div>
    </div>
</div>

<!-- Message Cost Calculation -->
<div class="form-section">
    <h3 class="section-title text-white">
        <i class="fas fa-money-bill stage-icon"></i>
        حساب التكلفة
    </h3>
    
    <div class="row">
        <div class="col-md-12">
            <div class="cost-calculator p-4 rounded" style="background: rgba(42, 42, 42, 0.6); border: 1px solid #555;">
                <div class="row text-center">
                    <div class="col-md-3">
                        <div class="cost-item">
                            <h4 class="text-orange" id="messageCountDisplay"><?php echo e($campaign->message_count ?? 0); ?></h4>
                            <p class="text-muted mb-0">عدد الرسائل</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="cost-item">
                            <h4 class="text-orange">0.25</h4>
                            <p class="text-muted mb-0">ريال/رسالة</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="cost-item">
                            <h4 class="text-orange" id="totalCost"><?php echo e(number_format(($campaign->message_count ?? 0) * 0.25, 2)); ?></h4>
                            <p class="text-muted mb-0">التكلفة الإجمالية</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="cost-item">
                            <h4 class="text-orange" id="totalWithVat"><?php echo e(number_format(($campaign->message_count ?? 0) * 0.25 * 1.15, 2)); ?></h4>
                            <p class="text-muted mb-0">المجموع مع الضريبة</p>
                        </div>
                    </div>
                </div>
                
                <hr style="border-color: #555;">
                
                <div class="text-center">
                    <p class="text-muted mb-2">
                        <i class="fas fa-info-circle me-2"></i>
                        سعر الرسالة الواحدة 0.25 ريال + ضريبة القيمة المضافة 15%
                    </p>
                    <p class="text-orange mb-0">
                        <strong>المبلغ النهائي: <span id="finalAmount"><?php echo e(number_format(($campaign->message_count ?? 0) * 0.25 * 1.15, 2)); ?></span> ريال</strong>
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Scheduling Options -->
<div class="form-section">
    <h3 class="section-title text-white">
        <i class="fas fa-clock stage-icon"></i>
        جدولة الإرسال
    </h3>
    
    <div class="row">
        <div class="col-md-6">
            <label for="send_immediately" class="form-label d-flex align-items-center text-white">
                <input type="radio" name="send_timing" id="send_immediately" 
                       class="form-check-input me-2" 
                       value="immediately" 
                       <?php echo e((!$campaign->scheduled_at || $campaign->send_timing == 'immediately') ? 'checked' : ''); ?>>
                إرسال فوري
            </label>
            <small class="text-muted">سيتم بدء إرسال الرسائل فور الموافقة على الحملة</small>
        </div>
        
        <div class="col-md-6">
            <label for="send_scheduled" class="form-label d-flex align-items-center text-white">
                <input type="radio" name="send_timing" id="send_scheduled" 
                       class="form-check-input me-2" 
                       value="scheduled" 
                       <?php echo e($campaign->scheduled_at && $campaign->send_timing == 'scheduled' ? 'checked' : ''); ?>>
                جدولة الإرسال
            </label>
            <small class="text-muted">تحديد وقت محدد لبدء إرسال الرسائل</small>
        </div>
    </div>
    
    <div class="row mt-3" id="scheduledTimeField" style="display: <?php echo e($campaign->scheduled_at ? 'block' : 'none'); ?>;">
        <div class="col-md-6">
            <label for="scheduled_date" class="form-label text-white">تاريخ الإرسال</label>
            <input type="date" name="scheduled_date" id="scheduled_date" 
                   class="form-control" 
                   value="<?php echo e($campaign->scheduled_at ? $campaign->scheduled_at->format('Y-m-d') : ''); ?>"
                   min="<?php echo e(date('Y-m-d')); ?>">
        </div>
        
        <div class="col-md-6">
            <label for="scheduled_time" class="form-label text-white">وقت الإرسال</label>
            <input type="time" name="scheduled_time" id="scheduled_time" 
                   class="form-control" 
                   value="<?php echo e($campaign->scheduled_at ? $campaign->scheduled_at->format('H:i') : '09:00'); ?>">
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const messageCountInput = document.getElementById('message_count');
    
    // Update cost calculation
    function updateCostCalculation() {
        const messageCount = parseInt(messageCountInput.value) || 0;
        const pricePerMessage = 0.25;
        
        const totalCost = messageCount * pricePerMessage;
        const totalWithVat = totalCost * 1.15;
        
        document.getElementById('messageCountDisplay').textContent = messageCount.toLocaleString();
        document.getElementById('totalCost').textContent = totalCost.toFixed(2);
        document.getElementById('totalWithVat').textContent = totalWithVat.toFixed(2);
        document.getElementById('finalAmount').textContent = totalWithVat.toFixed(2);
    }
    
    // Event listeners
    messageCountInput.addEventListener('input', updateCostCalculation);
    
    // Scheduling options
    const sendTimingRadios = document.querySelectorAll('[name="send_timing"]');
    const scheduledTimeField = document.getElementById('scheduledTimeField');
    
    sendTimingRadios.forEach(radio => {
        radio.addEventListener('change', function() {
            if (this.value === 'scheduled') {
                scheduledTimeField.style.display = 'block';
            } else {
                scheduledTimeField.style.display = 'none';
            }
        });
    });
    
    // Initial calculation
    updateCostCalculation();
});
</script> <?php /**PATH D:\GiftsSaudi-Laravel (1)\resources\views\admin\campaigns\stages\message-count.blade.php ENDPATH**/ ?>