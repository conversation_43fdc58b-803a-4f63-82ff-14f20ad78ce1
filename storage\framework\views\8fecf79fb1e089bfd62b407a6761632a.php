<?php $__env->startSection('title', 'عرض بيانات الموظف'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Header Section -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="fas fa-user-circle me-2"></i>
                        بيانات الموظف
                    </h1>
                    <p class="text-muted mb-0">عرض تفاصيل الموظف <?php echo e($employee->name); ?></p>
                </div>
                <div class="btn-group">
                    <a href="<?php echo e(route('vendor.employees.index')); ?>" class="btn btn-secondary">
                        <i class="fas fa-arrow-right me-2"></i>
                        العودة للقائمة
                    </a>
                    <a href="<?php echo e(route('vendor.employees.edit', $employee)); ?>" class="btn btn-warning">
                        <i class="fas fa-edit me-2"></i>
                        تعديل البيانات
                    </a>
                </div>
            </div>

            <div class="row">
                <!-- Employee Profile Card -->
                <div class="col-lg-4 mb-4">
                    <div class="card h-100">
                        <div class="card-body text-center">
                            <!-- Profile Photo -->
                            <div class="mb-3">
                                <?php if($employee->profile_photo): ?>
                                    <img src="<?php echo e(asset('storage/' . $employee->profile_photo)); ?>" 
                                         alt="صورة الموظف" 
                                         class="rounded-circle border border-3 border-light shadow"
                                         style="width: 120px; height: 120px; object-fit: cover;">
                                <?php else: ?>
                                    <div class="rounded-circle bg-primary d-inline-flex align-items-center justify-content-center text-white border border-3 border-light shadow"
                                         style="width: 120px; height: 120px; font-size: 3rem;">
                                        <?php echo e(substr($employee->name, 0, 1)); ?>

                                    </div>
                                <?php endif; ?>
                            </div>

                            <!-- Basic Info -->
                            <h4 class="card-title mb-1"><?php echo e($employee->name); ?></h4>
                            <p class="text-muted mb-2"><?php echo e($employee->position); ?></p>
                            <p class="text-muted small mb-3">رقم الموظف: <?php echo e($employee->employee_id); ?></p>

                            <!-- Status Badge -->
                            <div class="mb-3">
                                <?php switch($employee->status):
                                    case ('active'): ?>
                                        <span class="badge bg-success fs-6">
                                            <i class="fas fa-check-circle me-1"></i>
                                            <?php echo e($employee->status_display_name); ?>

                                        </span>
                                        <?php break; ?>
                                    <?php case ('pending'): ?>
                                        <span class="badge bg-warning fs-6">
                                            <i class="fas fa-clock me-1"></i>
                                            <?php echo e($employee->status_display_name); ?>

                                        </span>
                                        <?php break; ?>
                                    <?php case ('suspended'): ?>
                                        <span class="badge bg-danger fs-6">
                                            <i class="fas fa-ban me-1"></i>
                                            <?php echo e($employee->status_display_name); ?>

                                        </span>
                                        <?php break; ?>
                                    <?php default: ?>
                                        <span class="badge bg-secondary fs-6">
                                            <i class="fas fa-pause me-1"></i>
                                            <?php echo e($employee->status_display_name); ?>

                                        </span>
                                <?php endswitch; ?>
                            </div>

                            <!-- Quick Actions -->
                            <div class="d-grid gap-2">
                                <a href="tel:<?php echo e($employee->phone); ?>" class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-phone me-2"></i>
                                    اتصال
                                </a>
                                <a href="https://wa.me/<?php echo e(preg_replace('/[^0-9]/', '', $employee->phone)); ?>" 
                                   target="_blank" 
                                   class="btn btn-outline-success btn-sm">
                                    <i class="fab fa-whatsapp me-2"></i>
                                    واتساب
                                </a>
                                <a href="mailto:<?php echo e($employee->email); ?>" class="btn btn-outline-info btn-sm">
                                    <i class="fas fa-envelope me-2"></i>
                                    إيميل
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Employee Details -->
                <div class="col-lg-8 mb-4">
                    <div class="row">
                        <!-- Personal Information -->
                        <div class="col-12 mb-4">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">
                                        <i class="fas fa-user me-2"></i>
                                        المعلومات الشخصية
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label text-muted small">الاسم الكامل</label>
                                            <p class="mb-0 fw-bold"><?php echo e($employee->name); ?></p>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label text-muted small">رقم الهوية الوطنية</label>
                                            <p class="mb-0"><?php echo e($employee->national_id); ?></p>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label text-muted small">الجنس</label>
                                            <p class="mb-0"><?php echo e($employee->gender_display_name); ?></p>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label text-muted small">تاريخ الميلاد</label>
                                            <p class="mb-0">
                                                <?php echo e($employee->birth_date?->format('Y/m/d')); ?>

                                                <?php if($employee->age): ?>
                                                    <small class="text-muted">(<?php echo e($employee->age); ?> سنة)</small>
                                                <?php endif; ?>
                                            </p>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label text-muted small">رقم الهاتف</label>
                                            <p class="mb-0">
                                                <a href="tel:<?php echo e($employee->phone); ?>" class="text-decoration-none">
                                                    <?php echo e($employee->phone); ?>

                                                </a>
                                            </p>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label text-muted small">البريد الإلكتروني</label>
                                            <p class="mb-0">
                                                <a href="mailto:<?php echo e($employee->email); ?>" class="text-decoration-none">
                                                    <?php echo e($employee->email); ?>

                                                </a>
                                            </p>
                                        </div>
                                        <div class="col-12 mb-3">
                                            <label class="form-label text-muted small">العنوان</label>
                                            <p class="mb-0"><?php echo e($employee->address); ?></p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Employment Information -->
                        <div class="col-12 mb-4">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">
                                        <i class="fas fa-briefcase me-2"></i>
                                        معلومات الوظيفة
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label text-muted small">المنصب الوظيفي</label>
                                            <p class="mb-0 fw-bold"><?php echo e($employee->position); ?></p>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label text-muted small">نوع التوظيف</label>
                                            <p class="mb-0"><?php echo e($employee->employment_type_display_name); ?></p>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label text-muted small">تاريخ التوظيف</label>
                                            <p class="mb-0">
                                                <?php echo e($employee->hire_date?->format('Y/m/d')); ?>

                                                <?php if($employee->years_of_service > 0): ?>
                                                    <small class="text-muted">(<?php echo e($employee->years_of_service); ?> سنة خدمة)</small>
                                                <?php elseif($employee->months_of_service > 0): ?>
                                                    <small class="text-muted">(<?php echo e($employee->months_of_service); ?> شهر خدمة)</small>
                                                <?php endif; ?>
                                            </p>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label text-muted small">الراتب الشهري</label>
                                            <p class="mb-0"><?php echo e($employee->formatted_salary); ?></p>
                                        </div>
                                        <?php if($employee->last_login_at): ?>
                                            <div class="col-md-6 mb-3">
                                                <label class="form-label text-muted small">آخر تسجيل دخول</label>
                                                <p class="mb-0"><?php echo e($employee->last_login_at->format('Y/m/d H:i')); ?></p>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Emergency Contact -->
                        <div class="col-12 mb-4">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">
                                        <i class="fas fa-phone-alt me-2"></i>
                                        معلومات الاتصال الطارئ
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label text-muted small">اسم جهة الاتصال</label>
                                            <p class="mb-0"><?php echo e($employee->emergency_contact_name); ?></p>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label text-muted small">رقم الهاتف</label>
                                            <p class="mb-0">
                                                <a href="tel:<?php echo e($employee->emergency_contact_phone); ?>" class="text-decoration-none">
                                                    <?php echo e($employee->emergency_contact_phone); ?>

                                                </a>
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Performance Section -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-chart-line me-2"></i>
                                إحصائيات الأداء
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row text-center">
                                <div class="col-md-3 mb-3">
                                    <div class="border rounded p-3">
                                        <h3 class="text-primary mb-1"><?php echo e($performance['total_deliveries']); ?></h3>
                                        <p class="text-muted mb-0 small">إجمالي التسليمات</p>
                                    </div>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <div class="border rounded p-3">
                                        <h3 class="text-success mb-1"><?php echo e($performance['successful_deliveries']); ?></h3>
                                        <p class="text-muted mb-0 small">التسليمات الناجحة</p>
                                    </div>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <div class="border rounded p-3">
                                        <h3 class="text-warning mb-1"><?php echo e($performance['pending_deliveries']); ?></h3>
                                        <p class="text-muted mb-0 small">التسليمات المعلقة</p>
                                    </div>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <div class="border rounded p-3">
                                        <h3 class="text-info mb-1"><?php echo e(number_format($performance['success_rate'], 1)); ?>%</h3>
                                        <p class="text-muted mb-0 small">معدل النجاح</p>
                                    </div>
                                </div>
                            </div>

                            <?php if($employee->performance_rating > 0): ?>
                                <div class="row mt-4">
                                    <div class="col-12 text-center">
                                        <label class="form-label text-muted small">تقييم الأداء العام</label>
                                        <div class="mb-2">
                                            <?php for($i = 1; $i <= 5; $i++): ?>
                                                <?php if($i <= $employee->performance_rating): ?>
                                                    <i class="fas fa-star text-warning"></i>
                                                <?php else: ?>
                                                    <i class="far fa-star text-muted"></i>
                                                <?php endif; ?>
                                            <?php endfor; ?>
                                        </div>
                                        <p class="text-muted small"><?php echo e(number_format($employee->performance_rating, 1)); ?> من 5</p>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Deliveries Section -->
            <?php if($employee->deliveries->count() > 0): ?>
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-truck me-2"></i>
                                    آخر التسليمات
                                </h5>
                                <a href="<?php echo e(route('vendor.employees.deliveries', $employee)); ?>" class="btn btn-sm btn-outline-primary">
                                    عرض الكل
                                </a>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead class="table-light">
                                            <tr>
                                                <th>العميل</th>
                                                <th>الهدية</th>
                                                <th>الحالة</th>
                                                <th>التاريخ</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php $__currentLoopData = $employee->deliveries->take(5); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $delivery): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <tr>
                                                    <td>
                                                        <div>
                                                            <strong><?php echo e($delivery->client->name ?? 'غير محدد'); ?></strong>
                                                            <br>
                                                            <small class="text-muted"><?php echo e($delivery->client->phone ?? ''); ?></small>
                                                        </div>
                                                    </td>
                                                    <td><?php echo e($delivery->gift->name ?? 'غير محدد'); ?></td>
                                                    <td>
                                                        <?php switch($delivery->status):
                                                            case ('delivered'): ?>
                                                                <span class="badge bg-success">تم التسليم</span>
                                                                <?php break; ?>
                                                            <?php case ('pending'): ?>
                                                                <span class="badge bg-warning">معلق</span>
                                                                <?php break; ?>
                                                            <?php case ('cancelled'): ?>
                                                                <span class="badge bg-danger">ملغي</span>
                                                                <?php break; ?>
                                                            <?php default: ?>
                                                                <span class="badge bg-secondary"><?php echo e($delivery->status); ?></span>
                                                        <?php endswitch; ?>
                                                    </td>
                                                    <td><?php echo e($delivery->created_at->format('Y/m/d')); ?></td>
                                                </tr>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Notes Section -->
            <?php if($employee->notes): ?>
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-sticky-note me-2"></i>
                                    ملاحظات
                                </h5>
                            </div>
                            <div class="card-body">
                                <p class="mb-0"><?php echo e($employee->notes); ?></p>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Documents Section -->
            <?php if($employee->id_document || $employee->contract_document): ?>
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-file-alt me-2"></i>
                                    المستندات
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <?php if($employee->id_document): ?>
                                        <div class="col-md-6 mb-3">
                                            <div class="border rounded p-3 text-center">
                                                <i class="fas fa-id-card fa-2x text-primary mb-2"></i>
                                                <p class="mb-2">وثيقة الهوية</p>
                                                <a href="<?php echo e(asset('storage/' . $employee->id_document)); ?>"
                                                   target="_blank"
                                                   class="btn btn-sm btn-outline-primary">
                                                    <i class="fas fa-download me-1"></i>
                                                    تحميل
                                                </a>
                                            </div>
                                        </div>
                                    <?php endif; ?>
                                    <?php if($employee->contract_document): ?>
                                        <div class="col-md-6 mb-3">
                                            <div class="border rounded p-3 text-center">
                                                <i class="fas fa-file-contract fa-2x text-success mb-2"></i>
                                                <p class="mb-2">عقد العمل</p>
                                                <a href="<?php echo e(asset('storage/' . $employee->contract_document)); ?>"
                                                   target="_blank"
                                                   class="btn btn-sm btn-outline-success">
                                                    <i class="fas fa-download me-1"></i>
                                                    تحميل
                                                </a>
                                            </div>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.vendor', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\GiftsSaudi-Laravel (1)\resources\views/vendor/employees/show.blade.php ENDPATH**/ ?>