

<?php $__env->startSection('title', 'إدارة المدفوعات'); ?>

<?php $__env->startSection('content'); ?>
<style>
.payment-card {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    border: 1px solid #ff6b35;
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 20px;
    box-shadow: 0 10px 30px rgba(255, 107, 53, 0.1);
    transition: all 0.3s ease;
}

.payment-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(255, 107, 53, 0.2);
}

.payment-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #333;
}

.payment-id {
    color: #ff6b35;
    font-weight: 700;
    font-size: 1.1rem;
}

.payment-status {
    padding: 5px 15px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
}

.status-paid { background: rgba(40, 167, 69, 0.2); color: #28a745; }
.status-pending { background: rgba(255, 193, 7, 0.2); color: #ffc107; }
.status-failed { background: rgba(220, 53, 69, 0.2); color: #dc3545; }
.status-expired { background: rgba(108, 117, 125, 0.2); color: #6c757d; }
.status-cancelled { background: rgba(52, 58, 64, 0.2); color: #343a40; }
.status-initiated { background: rgba(23, 162, 184, 0.2); color: #17a2b8; }

.payment-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 15px;
}

.detail-item {
    background: rgba(42, 42, 42, 0.5);
    border-radius: 8px;
    padding: 12px;
}

.detail-label {
    color: #ccc;
    font-size: 0.8rem;
    text-transform: uppercase;
    margin-bottom: 5px;
}

.detail-value {
    color: white;
    font-weight: 600;
}

.amount-value {
    font-size: 1.5rem;
    font-weight: bold;
    color: #ff6b35;
}

.filter-section {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    border: 1px solid #333;
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 30px;
}

.stats-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    border: 1px solid #ff6b35;
    border-radius: 15px;
    padding: 25px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(255, 107, 53, 0.1);
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(255, 107, 53, 0.2);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(45deg, #ff6b35, #f7931e);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 15px;
    font-size: 1.5rem;
    color: white;
}

.stat-number {
    font-size: 2rem;
    font-weight: bold;
    color: #ff6b35;
    margin-bottom: 10px;
}

.stat-label {
    color: #ccc;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.btn-gradient {
    background: linear-gradient(45deg, #ff6b35, #f7931e);
    border: none;
    color: white;
    padding: 10px 20px;
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-gradient:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(255, 107, 53, 0.3);
    color: white;
}

.bulk-actions {
    background: rgba(255, 107, 53, 0.1);
    border: 1px solid rgba(255, 107, 53, 0.3);
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 20px;
    display: none;
}

.bulk-actions.show {
    display: block;
}

.form-control,
.form-select {
    background: rgba(42, 42, 42, 0.8);
    border: 1px solid #555;
    color: #fff;
    border-radius: 8px;
}

.form-control:focus,
.form-select:focus {
    background: rgba(42, 42, 42, 0.9);
    border-color: #ff6b35;
    box-shadow: 0 0 0 0.2rem rgba(255, 107, 53, 0.25);
    color: #fff;
}
</style>

<div class="page-header">
    <h1 class="page-title">
        <i class="fas fa-credit-card me-2"></i>
        إدارة المدفوعات
    </h1>
    <div class="page-actions">
        <a href="<?php echo e(route('admin.payments.analytics')); ?>" class="btn btn-outline-info me-2">
            <i class="fas fa-chart-line me-2"></i>
            التحليلات
        </a>
        <button onclick="exportPayments()" class="btn btn-gradient">
            <i class="fas fa-download me-2"></i>
            تصدير المدفوعات
        </button>
    </div>
</div>

<!-- Statistics Cards -->
<div class="stats-cards">
    <div class="stat-card">
        <div class="stat-icon">
            <i class="fas fa-receipt"></i>
        </div>
        <div class="stat-number"><?php echo e(number_format($stats['total'])); ?></div>
        <div class="stat-label">إجمالي المدفوعات</div>
    </div>
    
    <div class="stat-card">
        <div class="stat-icon">
            <i class="fas fa-check-circle"></i>
        </div>
        <div class="stat-number"><?php echo e(number_format($stats['paid'])); ?></div>
        <div class="stat-label">مدفوعات مكتملة</div>
    </div>
    
    <div class="stat-card">
        <div class="stat-icon">
            <i class="fas fa-clock"></i>
        </div>
        <div class="stat-number"><?php echo e(number_format($stats['pending'])); ?></div>
        <div class="stat-label">في الانتظار</div>
    </div>
    
    <div class="stat-card">
        <div class="stat-icon">
            <i class="fas fa-times-circle"></i>
        </div>
        <div class="stat-number"><?php echo e(number_format($stats['failed'])); ?></div>
        <div class="stat-label">مدفوعات فاشلة</div>
    </div>
    
    <div class="stat-card">
        <div class="stat-icon">
            <i class="fas fa-coins"></i>
        </div>
        <div class="stat-number"><?php echo e(number_format($stats['total_amount'], 2)); ?></div>
        <div class="stat-label">إجمالي المبلغ (ريال)</div>
    </div>
    
    <div class="stat-card">
        <div class="stat-icon">
            <i class="fas fa-calendar-day"></i>
        </div>
        <div class="stat-number"><?php echo e(number_format($stats['today_amount'], 2)); ?></div>
        <div class="stat-label">مدفوعات اليوم (ريال)</div>
    </div>
</div>

<!-- Filters Section -->
<div class="filter-section">
    <h4 class="text-white mb-3">
        <i class="fas fa-filter me-2"></i>
        تصفية المدفوعات
    </h4>
    
    <form method="GET" action="<?php echo e(route('admin.payments.index')); ?>" class="row g-3">
        <div class="col-md-3">
            <label class="form-label text-white">البحث</label>
            <input type="text" name="search" class="form-control" value="<?php echo e(request('search')); ?>" 
                   placeholder="رقم الدفع، اسم العميل، البريد الإلكتروني...">
        </div>
        
        <div class="col-md-2">
            <label class="form-label text-white">الحالة</label>
            <select name="status" class="form-select">
                <option value="">جميع الحالات</option>
                <option value="PAID" <?php echo e(request('status') == 'PAID' ? 'selected' : ''); ?>>مدفوعة</option>
                <option value="PENDING" <?php echo e(request('status') == 'PENDING' ? 'selected' : ''); ?>>في الانتظار</option>
                <option value="FAILED" <?php echo e(request('status') == 'FAILED' ? 'selected' : ''); ?>>فاشلة</option>
                <option value="EXPIRED" <?php echo e(request('status') == 'EXPIRED' ? 'selected' : ''); ?>>منتهية الصلاحية</option>
                <option value="CANCELLED" <?php echo e(request('status') == 'CANCELLED' ? 'selected' : ''); ?>>ملغية</option>
                <option value="INITIATED" <?php echo e(request('status') == 'INITIATED' ? 'selected' : ''); ?>>مبدأة</option>
            </select>
        </div>
        
        <div class="col-md-2">
            <label class="form-label text-white">من تاريخ</label>
            <input type="date" name="date_from" class="form-control" value="<?php echo e(request('date_from')); ?>">
        </div>
        
        <div class="col-md-2">
            <label class="form-label text-white">إلى تاريخ</label>
            <input type="date" name="date_to" class="form-control" value="<?php echo e(request('date_to')); ?>">
        </div>
        
        <div class="col-md-2">
            <label class="form-label text-white">طريقة الدفع</label>
            <select name="payment_method" class="form-select">
                <option value="">جميع الطرق</option>
                <option value="VISA" <?php echo e(request('payment_method') == 'VISA' ? 'selected' : ''); ?>>فيزا</option>
                <option value="MADA" <?php echo e(request('payment_method') == 'MADA' ? 'selected' : ''); ?>>مدى</option>
                <option value="MASTERCARD" <?php echo e(request('payment_method') == 'MASTERCARD' ? 'selected' : ''); ?>>ماستركارد</option>
                <option value="APPLEPAY" <?php echo e(request('payment_method') == 'APPLEPAY' ? 'selected' : ''); ?>>آبل باي</option>
            </select>
        </div>
        
        <div class="col-md-1">
            <label class="form-label">&nbsp;</label>
            <div class="d-grid">
                <button type="submit" class="btn btn-gradient">
                    <i class="fas fa-search"></i>
                </button>
            </div>
        </div>
    </form>
</div>

<!-- Bulk Actions -->
<div class="bulk-actions" id="bulkActions">
    <form id="bulkActionForm" method="POST" action="<?php echo e(route('admin.payments.bulk-action')); ?>">
        <?php echo csrf_field(); ?>
        <div class="row align-items-end">
            <div class="col-md-3">
                <label class="form-label text-white">الإجراء الجماعي</label>
                <select name="action" class="form-select" required>
                    <option value="">اختر إجراء</option>
                    <option value="mark_paid">تعيين كمدفوعة</option>
                    <option value="mark_failed">تعيين كفاشلة</option>
                    <option value="delete">حذف المحدد</option>
                </select>
            </div>
            <div class="col-md-3">
                <button type="submit" class="btn btn-gradient">
                    <i class="fas fa-play me-2"></i>
                    تنفيذ الإجراء
                </button>
                <button type="button" class="btn btn-outline-secondary ms-2" onclick="hideBulkActions()">
                    إلغاء
                </button>
            </div>
            <div class="col-md-6 text-end">
                <span class="text-white">
                    <span id="selectedCount">0</span> عنصر محدد
                </span>
            </div>
        </div>
    </form>
</div>

<!-- Payments List -->
<div class="row">
    <?php $__empty_1 = true; $__currentLoopData = $payments; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $payment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
        <div class="col-md-6 col-lg-4 mb-4">
            <div class="payment-card">
                <div class="d-flex justify-content-between align-items-start mb-3">
                    <input type="checkbox" name="selected_payments[]" value="<?php echo e($payment->id); ?>" 
                           class="payment-checkbox" onchange="updateBulkActions()">
                    <span class="payment-status status-<?php echo e(strtolower($payment->payment_status)); ?>">
                        <?php echo e($payment->status_display_name); ?>

                    </span>
                </div>
                
                <div class="payment-header">
                    <div>
                        <div class="payment-id"><?php echo e($payment->payment_id); ?></div>
                        <small class="text-muted"><?php echo e($payment->order_reference); ?></small>
                    </div>
                    <div class="amount-value"><?php echo e($payment->formatted_amount); ?></div>
                </div>
                
                <div class="payment-details">
                    <div class="detail-item">
                        <div class="detail-label">اسم العميل</div>
                        <div class="detail-value"><?php echo e($payment->customer_name); ?></div>
                    </div>
                    
                    <div class="detail-item">
                        <div class="detail-label">البريد الإلكتروني</div>
                        <div class="detail-value"><?php echo e($payment->customer_email); ?></div>
                    </div>
                    
                    <div class="detail-item">
                        <div class="detail-label">رقم الهاتف</div>
                        <div class="detail-value"><?php echo e($payment->customer_phone); ?></div>
                    </div>
                    
                    <div class="detail-item">
                        <div class="detail-label">طريقة الدفع</div>
                        <div class="detail-value"><?php echo e($payment->payment_method ?? 'غير محدد'); ?></div>
                    </div>
                    
                    <div class="detail-item">
                        <div class="detail-label">التاريخ</div>
                        <div class="detail-value"><?php echo e($payment->created_at->format('Y/m/d H:i')); ?></div>
                    </div>
                    
                    <div class="detail-item">
                        <div class="detail-label">رقم المعاملة</div>
                        <div class="detail-value"><?php echo e($payment->transaction_id ?? 'غير محدد'); ?></div>
                    </div>
                </div>
                
                <div class="d-flex justify-content-between align-items-center mt-3">
                    <div class="btn-group">
                        <a href="<?php echo e(route('admin.payments.show', $payment)); ?>" 
                           class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-eye"></i> عرض
                        </a>
                        
                        <?php if(!$payment->isPaid()): ?>
                            <button type="button" class="btn btn-sm btn-outline-success" 
                                    onclick="updatePaymentStatus(<?php echo e($payment->id); ?>, 'PAID')">
                                <i class="fas fa-check"></i> تأكيد
                            </button>
                        <?php endif; ?>
                        
                        <?php if($payment->isPending()): ?>
                            <button type="button" class="btn btn-sm btn-outline-danger" 
                                    onclick="updatePaymentStatus(<?php echo e($payment->id); ?>, 'FAILED')">
                                <i class="fas fa-times"></i> رفض
                            </button>
                        <?php endif; ?>
                    </div>
                    
                    <button type="button" class="btn btn-sm btn-outline-danger" 
                            onclick="deletePayment(<?php echo e($payment->id); ?>)">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        </div>
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
        <div class="col-12">
            <div class="text-center py-5">
                <i class="fas fa-credit-card fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">لا توجد مدفوعات</h5>
                <p class="text-muted">لا توجد مدفوعات متاحة للعرض.</p>
            </div>
        </div>
    <?php endif; ?>
</div>

<!-- Pagination -->
<?php if($payments->hasPages()): ?>
    <div class="d-flex justify-content-center mt-4">
        <?php echo e($payments->appends(request()->query())->links()); ?>

    </div>
<?php endif; ?>

<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
function exportPayments() {
    const form = document.createElement('form');
    form.method = 'GET';
    form.action = '<?php echo e(route("admin.payments.export")); ?>';
    
    // Add current filter parameters
    const params = new URLSearchParams(window.location.search);
    for (const [key, value] of params) {
        const input = document.createElement('input');
        input.type = 'hidden';
        input.name = key;
        input.value = value;
        form.appendChild(input);
    }
    
    document.body.appendChild(form);
    form.submit();
    document.body.removeChild(form);
}

function updatePaymentStatus(paymentId, status) {
    if (confirm('هل أنت متأكد من تحديث حالة الدفع؟')) {
        fetch(`/admin/payments/${paymentId}/update-status`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({ status: status })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('حدث خطأ أثناء تحديث حالة الدفع.');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ أثناء تحديث حالة الدفع.');
        });
    }
}

function deletePayment(paymentId) {
    if (confirm('هل أنت متأكد من حذف هذه الدفعة؟')) {
        fetch(`/admin/payments/${paymentId}`, {
            method: 'DELETE',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('حدث خطأ أثناء حذف الدفعة.');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ أثناء حذف الدفعة.');
        });
    }
}

function updateBulkActions() {
    const checkboxes = document.querySelectorAll('.payment-checkbox:checked');
    const bulkActions = document.getElementById('bulkActions');
    const selectedCount = document.getElementById('selectedCount');
    
    selectedCount.textContent = checkboxes.length;
    
    if (checkboxes.length > 0) {
        bulkActions.classList.add('show');
    } else {
        bulkActions.classList.remove('show');
    }
}

function hideBulkActions() {
    document.querySelectorAll('.payment-checkbox').forEach(cb => cb.checked = false);
    updateBulkActions();
}

document.getElementById('bulkActionForm').addEventListener('submit', function(e) {
    const checkboxes = document.querySelectorAll('.payment-checkbox:checked');
    
    if (checkboxes.length === 0) {
        e.preventDefault();
        alert('يرجى تحديد مدفوعات للتنفيذ عليها.');
        return;
    }
    
    // Add selected payment IDs to form
    checkboxes.forEach(checkbox => {
        const input = document.createElement('input');
        input.type = 'hidden';
        input.name = 'payments[]';
        input.value = checkbox.value;
        this.appendChild(input);
    });
});
</script>
<?php $__env->stopPush(); ?> 
<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\GiftsSaudi-Laravel (1)\resources\views/admin/payments/index.blade.php ENDPATH**/ ?>