

<?php $__env->startSection('page-title', 'إدارة التجار'); ?>

<?php $__env->startPush('styles'); ?>
<style>
    /* Professional Vendors Management Styles */
    .vendors-container {
        background: transparent;
    }

    /* Page Header */
    .page-header {
        background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
        border: 1px solid rgba(255, 107, 53, 0.2);
        border-radius: 25px;
        padding: 30px;
        margin-bottom: 30px;
        position: relative;
        overflow: hidden;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4);
    }

    .page-header::before {
        content: '';
        position: absolute;
        top: -50%;
        right: -50%;
        width: 200%;
        height: 200%;
        background: linear-gradient(45deg, transparent, rgba(255, 107, 53, 0.1), transparent);
        transform: rotate(45deg);
        animation: headerShimmer 4s ease-in-out infinite;
    }

    @keyframes headerShimmer {
        0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
        100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
    }

    .page-title {
        color: white;
        font-weight: 800;
        font-size: 2.2rem;
        margin-bottom: 10px;
        background: linear-gradient(135deg, #ffffff 0%, #ff6b35 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        position: relative;
        z-index: 2;
    }

    .page-subtitle {
        color: #b0b0b0;
        font-size: 1rem;
        position: relative;
        z-index: 2;
    }

    /* Statistics Cards */
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 25px;
        margin-bottom: 40px;
    }

    .stat-card {
        background: linear-gradient(145deg, #2d2d2d, #1a1a1a);
        border: 1px solid rgba(255, 107, 53, 0.1);
        border-radius: 20px;
        padding: 30px;
        position: relative;
        overflow: hidden;
        transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
    }

    .stat-card:hover {
        transform: translateY(-8px) scale(1.02);
        box-shadow: 0 25px 50px rgba(0, 0, 0, 0.4);
        border-color: rgba(255, 107, 53, 0.3);
    }

    .stat-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: var(--card-gradient);
        border-radius: 20px 20px 0 0;
    }

    .stat-card.primary::before { background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%); }
    .stat-card.success::before { background: linear-gradient(135deg, #00d4aa 0%, #00bf95 100%); }
    .stat-card.info::before { background: linear-gradient(135deg, #3867d6 0%, #8e44ad 100%); }
    .stat-card.warning::before { background: linear-gradient(135deg, #ffc107 0%, #ff8f00 100%); }

    .stat-content {
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .stat-info h4 {
        font-size: 2.2rem;
        font-weight: 800;
        color: white;
        margin-bottom: 5px;
    }

    .stat-info p {
        color: #b0b0b0;
        font-size: 0.9rem;
        font-weight: 600;
        margin: 0;
    }

    .stat-icon {
        width: 60px;
        height: 60px;
        border-radius: 15px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.8rem;
        color: white;
        background: var(--card-gradient);
        box-shadow: 0 10px 25px rgba(255, 107, 53, 0.3);
    }

    .stat-card.primary .stat-icon { 
        background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
        box-shadow: 0 10px 25px rgba(255, 107, 53, 0.3);
    }
    .stat-card.success .stat-icon { 
        background: linear-gradient(135deg, #00d4aa 0%, #00bf95 100%);
        box-shadow: 0 10px 25px rgba(0, 212, 170, 0.3);
    }
    .stat-card.info .stat-icon { 
        background: linear-gradient(135deg, #3867d6 0%, #8e44ad 100%);
        box-shadow: 0 10px 25px rgba(56, 103, 214, 0.3);
    }
    .stat-card.warning .stat-icon { 
        background: linear-gradient(135deg, #ffc107 0%, #ff8f00 100%);
        box-shadow: 0 10px 25px rgba(255, 193, 7, 0.3);
    }

    /* Filters Card */
    .filter-card {
        background: linear-gradient(145deg, #2d2d2d, #1a1a1a);
        border: 1px solid rgba(255, 107, 53, 0.1);
        border-radius: 20px;
        padding: 25px;
        margin-bottom: 30px;
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
    }

    .filter-card .form-label {
        color: #e0e0e0;
        font-weight: 600;
        margin-bottom: 8px;
    }

    .filter-card .form-control,
    .filter-card .form-select {
        background: rgba(255, 255, 255, 0.05);
        border: 1px solid rgba(255, 107, 53, 0.2);
        border-radius: 12px;
        color: white;
        padding: 12px 15px;
    }

    .filter-card .form-control:focus,
    .filter-card .form-select:focus {
        background: rgba(255, 255, 255, 0.08);
        border-color: var(--accent-color);
        box-shadow: 0 0 0 0.2rem rgba(255, 107, 53, 0.25);
        color: white;
    }

    .filter-card .form-control::placeholder {
        color: #888;
    }

    .filter-card .btn-primary {
        background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
        border: none;
        border-radius: 12px;
        padding: 12px 20px;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .filter-card .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(255, 107, 53, 0.4);
    }

    .filter-card .btn-outline-secondary {
        border: 1px solid rgba(255, 255, 255, 0.2);
        color: #b0b0b0;
        border-radius: 12px;
        padding: 12px 20px;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .filter-card .btn-outline-secondary:hover {
        background: rgba(255, 255, 255, 0.1);
        border-color: rgba(255, 255, 255, 0.3);
        color: white;
    }

    /* Vendors Table */
    .vendors-table-card {
        background: linear-gradient(145deg, #2d2d2d, #1a1a1a);
        border: 1px solid rgba(255, 107, 53, 0.1);
        border-radius: 20px;
        overflow: hidden;
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
    }

    .vendors-table-card .table {
        margin: 0;
        color: #e0e0e0;
    }

    .vendors-table-card .table thead th {
        background: linear-gradient(135deg, #1a1a1a 0%, #0f0f0f 100%);
        border: none;
        color: white !important;
        font-weight: 700;
        padding: 20px 15px;
        font-size: 0.9rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .vendors-table-card .table tbody tr {
        border-bottom: 1px solid rgba(255, 107, 53, 0.1);
        transition: all 0.3s ease;
    }

    .vendors-table-card .table tbody tr:hover {
        background: rgba(255, 107, 53, 0.05);
    }

    .vendors-table-card .table tbody td {
        border: none;
        padding: 20px 15px;
        vertical-align: middle;
    }

    /* Vendor Logo */
    .vendor-logo {
        width: 50px;
        height: 50px;
        border-radius: 12px;
        object-fit: cover;
        background: linear-gradient(135deg, #2d2d2d, #1a1a1a);
        border: 1px solid rgba(255, 107, 53, 0.2);
        margin-left: 10px;
    }

    .vendor-avatar {
        width: 50px;
        height: 50px;
        border-radius: 12px;
        background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.2rem;
        font-weight: bold;
        margin-left: 10px;
    }

    /* Status Badges */
    .status-badge {
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 0.75rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .status-approved { 
        background: linear-gradient(135deg, rgba(0, 212, 170, 0.2), rgba(0, 191, 149, 0.2)); 
        color: #00d4aa; 
        border: 1px solid rgba(0, 212, 170, 0.3);
    }
    .status-pending { 
        background: linear-gradient(135deg, rgba(255, 193, 7, 0.2), rgba(255, 143, 0, 0.2)); 
        color: #ffc107; 
        border: 1px solid rgba(255, 193, 7, 0.3);
    }
    .status-rejected { 
        background: linear-gradient(135deg, rgba(255, 71, 87, 0.2), rgba(196, 69, 105, 0.2)); 
        color: #ff4757; 
        border: 1px solid rgba(255, 71, 87, 0.3);
    }

    /* Vendor Details */
    .vendor-details {
        font-size: 0.85rem;
        color: #b0b0b0;
        line-height: 1.4;
    }

    .vendor-name {
        color: #000000 !important;
        font-weight: 600;
        font-size: 0.95rem;
        margin-bottom: 3px;
    }

    .vendor-contact {
        color: #888;
        font-size: 0.8rem;
    }

    /* Action Buttons */
    .action-buttons .btn {
        margin: 0 2px;
        padding: 8px 12px;
        font-size: 0.8rem;
        border-radius: 8px;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .action-buttons .btn-outline-primary {
        border-color: rgba(255, 107, 53, 0.5);
        color: #ff6b35;
    }

    .action-buttons .btn-outline-primary:hover {
        background: #ff6b35;
        border-color: #ff6b35;
        color: white;
        transform: translateY(-2px);
    }

    .action-buttons .btn-outline-success {
        border-color: rgba(0, 212, 170, 0.5);
        color: #00d4aa;
    }

    .action-buttons .btn-outline-success:hover {
        background: #00d4aa;
        border-color: #00d4aa;
        color: white;
        transform: translateY(-2px);
    }

    .action-buttons .btn-outline-warning {
        border-color: rgba(255, 193, 7, 0.5);
        color: #ffc107;
    }

    .action-buttons .btn-outline-warning:hover {
        background: #ffc107;
        border-color: #ffc107;
        color: white;
        transform: translateY(-2px);
    }

    .action-buttons .btn-outline-danger {
        border-color: rgba(255, 71, 87, 0.5);
        color: #ff4757;
    }

    .action-buttons .btn-outline-danger:hover {
        background: #ff4757;
        border-color: #ff4757;
        color: white;
        transform: translateY(-2px);
    }

    /* Add Vendor Button */
    .add-vendor-btn {
        background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
        border: none;
        border-radius: 15px;
        padding: 12px 25px;
        font-weight: 700;
        color: white;
        text-decoration: none;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        gap: 8px;
    }

    .add-vendor-btn:hover {
        color: white;
        transform: translateY(-3px);
        box-shadow: 0 10px 30px rgba(255, 107, 53, 0.4);
    }

    /* Text colors for consistency */
    .text-secondary,
    .text-body-secondary,
    small,
    .form-text {
        color: #b0b0b0 !important;
    }

    .text-muted {
        color: #888 !important;
    }

    /* Pagination */
    .pagination {
        justify-content: center;
        margin-top: 30px;
    }

    .pagination .page-link {
        background: rgba(255, 255, 255, 0.05);
        border: 1px solid rgba(255, 107, 53, 0.2);
        color: #b0b0b0;
        margin: 0 2px;
        border-radius: 8px;
        padding: 10px 15px;
        transition: all 0.3s ease;
    }

    .pagination .page-link:hover {
        background: rgba(255, 107, 53, 0.1);
        border-color: var(--accent-color);
        color: white;
        transform: translateY(-2px);
    }

    .pagination .page-item.active .page-link {
        background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
        border-color: var(--accent-color);
        color: white;
    }

    /* Statistics formatting */
    .vendor-stats {
        font-size: 0.85rem;
        color: #b0b0b0;
    }

    .vendor-stats .text-success {
        color: #00d4aa !important;
    }

    .vendor-stats .text-warning {
        color: #ffc107 !important;
    }

    .vendor-stats .text-info {
        color: #3867d6 !important;
    }
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
<div class="vendors-container">
    <!-- Success/Error Messages -->
    <?php if(session('success')): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert" style="border-radius: 15px; margin-bottom: 25px; background: linear-gradient(135deg, rgba(0, 212, 170, 0.15) 0%, rgba(0, 191, 149, 0.15) 100%); border: 1px solid rgba(0, 212, 170, 0.3); color: #00d4aa;">
            <i class="fas fa-check-circle me-2"></i>
            <?php echo e(session('success')); ?>

            <button type="button" class="btn-close" data-bs-dismiss="alert" style="filter: brightness(0) invert(1);"></button>
        </div>
    <?php endif; ?>

    <?php if(session('error')): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert" style="border-radius: 15px; margin-bottom: 25px; background: linear-gradient(135deg, rgba(255, 71, 87, 0.15) 0%, rgba(196, 69, 105, 0.15) 100%); border: 1px solid rgba(255, 71, 87, 0.3); color: #ff4757;">
            <i class="fas fa-exclamation-circle me-2"></i>
            <?php echo e(session('error')); ?>

            <button type="button" class="btn-close" data-bs-dismiss="alert" style="filter: brightness(0) invert(1);"></button>
        </div>
    <?php endif; ?>

    <!-- Page Header -->
    <div class="page-header">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="page-title">إدارة التجار</h1>
                <p class="page-subtitle">تتبع وإدارة شركاء التجار والموردين</p>
            </div>
            <a href="<?php echo e(route('admin.vendors.create')); ?>" class="add-vendor-btn">
                <i class="fas fa-plus"></i>
                إضافة تاجر جديد
            </a>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="stats-grid">
        <div class="stat-card primary">
            <div class="stat-content">
                <div class="stat-info">
                    <h4><?php echo e($stats['total']); ?></h4>
                    <p>إجمالي التجار</p>
                </div>
                <div class="stat-icon">
                    <i class="fas fa-store"></i>
                </div>
            </div>
        </div>
        
        <div class="stat-card success">
            <div class="stat-content">
                <div class="stat-info">
                    <h4><?php echo e($stats['approved']); ?></h4>
                    <p>معتمد</p>
                </div>
                <div class="stat-icon">
                    <i class="fas fa-check"></i>
                </div>
            </div>
        </div>
        
        <div class="stat-card warning">
            <div class="stat-content">
                <div class="stat-info">
                    <h4><?php echo e($stats['pending']); ?></h4>
                    <p>في الانتظار</p>
                </div>
                <div class="stat-icon">
                    <i class="fas fa-clock"></i>
                </div>
            </div>
        </div>
        
        <div class="stat-card info">
            <div class="stat-content">
                <div class="stat-info">
                    <h4><?php echo e($stats['total'] > 0 ? round(($stats['approved'] / $stats['total']) * 100) : 0); ?>%</h4>
                    <p>معدل القبول</p>
                </div>
                <div class="stat-icon">
                    <i class="fas fa-percentage"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="filter-card">
        <form method="GET" action="<?php echo e(route('admin.vendors.index')); ?>" class="row g-3">
            <div class="col-md-4">
                <label class="form-label">البحث</label>
                <input type="text" class="form-control" name="search" 
                       value="<?php echo e(request('search')); ?>" 
                       placeholder="اسم الشركة، المستخدم، البريد...">
            </div>
            
            <div class="col-md-2">
                <label class="form-label">الحالة</label>
                <select class="form-select" name="status">
                    <option value="">جميع الحالات</option>
                    <option value="approved" <?php echo e(request('status') === 'approved' ? 'selected' : ''); ?>>معتمد</option>
                    <option value="pending" <?php echo e(request('status') === 'pending' ? 'selected' : ''); ?>>في الانتظار</option>
                </select>
            </div>
            
            <div class="col-md-2">
                <label class="form-label">الفئة</label>
                <select class="form-select" name="category">
                    <option value="">جميع الفئات</option>
                    <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <option value="<?php echo e($category); ?>" <?php echo e(request('category') === $category ? 'selected' : ''); ?>>
                            <?php echo e($category); ?>

                        </option>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </select>
            </div>
            
            <div class="col-md-2">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search"></i> بحث
                    </button>
                </div>
            </div>
            
            <div class="col-md-2">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <a href="<?php echo e(route('admin.vendors.index')); ?>" class="btn btn-outline-secondary">
                        <i class="fas fa-times"></i> مسح
                    </a>
                </div>
            </div>
        </form>
    </div>

    <!-- Vendors Table -->
    <div class="vendors-table-card">
        <div class="table-responsive">
            <table class="table">
                <thead>
                    <tr>
                        <th>التاجر</th>
                        <th>معلومات الاتصال</th>
                        <th>الإحصائيات</th>
                        <th>الحالة</th>
                        <th>تاريخ الانضمام</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    <?php $__empty_1 = true; $__currentLoopData = $vendors; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $vendor): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <tr>
                            <td>
                                <div class="d-flex align-items-center">
                                    <?php if($vendor->logo): ?>
                                        <img src="<?php echo e(asset('storage/' . $vendor->logo)); ?>" alt="<?php echo e($vendor->company_name); ?>" class="vendor-logo">
                                    <?php else: ?>
                                        <div class="vendor-avatar">
                                            <?php echo e(mb_substr($vendor->company_name ?? $vendor->user->name ?? 'ت', 0, 1)); ?>

                                        </div>
                                    <?php endif; ?>
                                    <div>
                                        <div class="vendor-name"><?php echo e($vendor->company_name ?? 'غير محدد'); ?></div>
                                        <div class="vendor-contact"><?php echo e($vendor->user->name ?? 'غير محدد'); ?></div>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div class="vendor-details">
                                    <?php if($vendor->user->email): ?>
                                        <div><i class="fas fa-envelope text-orange"></i> <?php echo e($vendor->user->email); ?></div>
                                    <?php endif; ?>
                                    <?php if($vendor->phone): ?>
                                        <div><i class="fas fa-phone text-orange"></i> <?php echo e($vendor->phone); ?></div>
                                    <?php endif; ?>
                                    <?php if($vendor->address): ?>
                                        <div><i class="fas fa-map-marker-alt text-orange"></i> <?php echo e(Str::limit($vendor->address, 30)); ?></div>
                                    <?php endif; ?>
                                </div>
                            </td>
                            <td>
                                <div class="vendor-stats">
                                    <div><i class="fas fa-gifts text-orange"></i> <?php echo e($vendor->gifts_count ?? 0); ?> هدية</div>
                                    <div><i class="fas fa-chart-line text-success"></i> <?php echo e($vendor->orders_count ?? 0); ?> طلب</div>
                                    <div><i class="fas fa-star text-warning"></i> <?php echo e(number_format($vendor->rating ?? 0, 1)); ?>/5</div>
                                </div>
                            </td>
                            <td>
                                <span class="status-badge status-<?php echo e($vendor->is_approved ? 'approved' : 'pending'); ?>">
                                    <?php if($vendor->is_approved): ?> معتمد
                                    <?php else: ?> في الانتظار
                                    <?php endif; ?>
                                </span>
                            </td>
                            <td>
                                <div class="text-white"><?php echo e($vendor->created_at->format('Y/m/d')); ?></div>
                                <small class="text-muted"><?php echo e($vendor->created_at->diffForHumans()); ?></small>
                            </td>
                            <td>
                                <div class="action-buttons">
                                    <a href="<?php echo e(route('admin.vendors.show', $vendor)); ?>" class="btn btn-outline-primary btn-sm" title="عرض">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="<?php echo e(route('admin.vendors.edit', $vendor)); ?>" class="btn btn-outline-success btn-sm" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <?php if(!$vendor->is_approved): ?>
                                        <form method="POST" action="<?php echo e(route('admin.vendors.approve', $vendor)); ?>" class="d-inline">
                                            <?php echo csrf_field(); ?>
                                            <button type="submit" class="btn btn-outline-warning btn-sm" title="اعتماد">
                                                <i class="fas fa-check"></i>
                                            </button>
                                        </form>
                                    <?php else: ?>
                                        <form method="POST" action="<?php echo e(route('admin.vendors.reject', $vendor)); ?>" class="d-inline">
                                            <?php echo csrf_field(); ?>
                                            <button type="submit" class="btn btn-outline-danger btn-sm" title="رفض" 
                                                    onclick="return confirm('هل أنت متأكد من رفض هذا التاجر؟')">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        </form>
                                    <?php endif; ?>
                                    <form method="POST" action="<?php echo e(route('admin.vendors.destroy', $vendor)); ?>" class="d-inline">
                                        <?php echo csrf_field(); ?>
                                        <?php echo method_field('DELETE'); ?>
                                        <button type="submit" class="btn btn-outline-danger btn-sm" 
                                                onclick="return confirm('هل أنت متأكد من حذف هذا التاجر؟')" title="حذف">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <tr>
                            <td colspan="6" class="text-center text-secondary py-5">
                                <i class="fas fa-store fa-3x mb-3 text-orange"></i>
                                <div>لا توجد تجار مطابقون للمعايير المحددة</div>
                            </td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>

    <!-- Pagination -->
    <?php if(method_exists($vendors, 'links')): ?>
        <div class="d-flex justify-content-center">
            <?php echo e($vendors->links()); ?>

        </div>
    <?php endif; ?>
</div>
<?php $__env->stopSection(); ?> 
<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\GiftsSaudi-Laravel (1)\resources\views\admin\vendors\index.blade.php ENDPATH**/ ?>