

<?php $__env->startSection('title', 'الحملات الإعلانية'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-header">
                <h1 class="page-title">
                    <i class="fas fa-bullhorn me-2"></i>
                    إدارة الحملات الإعلانية
                </h1>
                <p class="page-subtitle">متابعة وإدارة جميع حملاتك الإعلانية والترويجية</p>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="<?php echo e(route('vendor.dashboard')); ?>">لوحة التحكم</a></li>
                        <li class="breadcrumb-item active" aria-current="page">الحملات الإعلانية</li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="stat-card total">
                <div class="stat-icon">
                    <i class="fas fa-bullhorn"></i>
                </div>
                <div class="stat-content">
                    <div class="stat-number"><?php echo e($stats['total']); ?></div>
                    <div class="stat-label">إجمالي الحملات</div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card pending">
                <div class="stat-icon">
                    <i class="fas fa-clock"></i>
                </div>
                <div class="stat-content">
                    <div class="stat-number"><?php echo e($stats['pending_approval']); ?></div>
                    <div class="stat-label">في انتظار الموافقة</div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card progress">
                <div class="stat-icon">
                    <i class="fas fa-play-circle"></i>
                </div>
                <div class="stat-content">
                    <div class="stat-number"><?php echo e($stats['in_progress']); ?></div>
                    <div class="stat-label">قيد التنفيذ</div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card completed">
                <div class="stat-icon">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="stat-content">
                    <div class="stat-number"><?php echo e($stats['completed']); ?></div>
                    <div class="stat-label">مكتملة</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Action Bar -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="action-bar">
                <div class="d-flex justify-content-between align-items-center">
                    <div class="action-buttons">
                        <a href="<?php echo e(route('vendor.campaigns.create')); ?>" class="btn btn-primary btn-lg">
                            <i class="fas fa-plus me-2"></i>
                            إنشاء حملة جديدة
                        </a>
                        
                        <a href="<?php echo e(route('vendor.campaigns.performance')); ?>" class="btn btn-outline-info">
                            <i class="fas fa-chart-line me-2"></i>
                            تقرير الأداء
                        </a>
                    </div>
                    
                    <div class="view-options">
                        <div class="btn-group" role="group">
                            <input type="radio" class="btn-check" name="view" id="grid-view" checked>
                            <label class="btn btn-outline-secondary" for="grid-view">
                                <i class="fas fa-th"></i>
                            </label>
                            
                            <input type="radio" class="btn-check" name="view" id="list-view">
                            <label class="btn btn-outline-secondary" for="list-view">
                                <i class="fas fa-list"></i>
                            </label>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Campaigns Grid -->
    <div class="row" id="campaigns-container">
        <?php $__empty_1 = true; $__currentLoopData = $campaigns; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $campaign): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
            <div class="col-lg-6 col-xl-4 mb-4 campaign-card">
                <div class="card campaign-item">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-start">
                            <div class="campaign-info">
                                <h5 class="campaign-name"><?php echo e($campaign->campaign_name); ?></h5>
                                <p class="campaign-type">
                                    <?php switch($campaign->campaign_type):
                                        case ('promotional'): ?>
                                            <i class="fas fa-percent me-1"></i>ترويجية
                                            <?php break; ?>
                                        <?php case ('seasonal'): ?>
                                            <i class="fas fa-calendar-alt me-1"></i>موسمية
                                            <?php break; ?>
                                        <?php case ('loyalty'): ?>
                                            <i class="fas fa-heart me-1"></i>الولاء
                                            <?php break; ?>
                                        <?php case ('birthday'): ?>
                                            <i class="fas fa-birthday-cake me-1"></i>عيد ميلاد
                                            <?php break; ?>
                                        <?php case ('appreciation'): ?>
                                            <i class="fas fa-thumbs-up me-1"></i>تقدير وشكر
                                            <?php break; ?>
                                    <?php endswitch; ?>
                                </p>
                            </div>
                            
                            <div class="campaign-status">
                                <?php switch($campaign->stage):
                                    case ('template_selection'): ?>
                                        <span class="badge bg-warning">اختيار القالب</span>
                                        <?php break; ?>
                                    <?php case ('client_filtering'): ?>
                                        <span class="badge bg-info">فلترة العملاء</span>
                                        <?php break; ?>
                                    <?php case ('message_count'): ?>
                                        <span class="badge bg-primary">عدد الرسائل</span>
                                        <?php break; ?>
                                    <?php case ('platform_selection'): ?>
                                        <span class="badge bg-secondary">اختيار المنصة</span>
                                        <?php break; ?>
                                    <?php case ('pending_approval'): ?>
                                        <span class="badge bg-warning">انتظار الموافقة</span>
                                        <?php break; ?>
                                    <?php case ('approved'): ?>
                                        <span class="badge bg-success">معتمدة</span>
                                        <?php break; ?>
                                    <?php case ('in_progress'): ?>
                                        <span class="badge bg-info">قيد التنفيذ</span>
                                        <?php break; ?>
                                    <?php case ('completed'): ?>
                                        <span class="badge bg-success">مكتملة</span>
                                        <?php break; ?>
                                    <?php default: ?>
                                        <span class="badge bg-light text-dark">جديدة</span>
                                <?php endswitch; ?>
                            </div>
                        </div>
                    </div>

                    <div class="card-body">
                        <div class="campaign-details">
                            <div class="detail-item">
                                <span class="detail-label">
                                    <i class="fas fa-gift me-1"></i>
                                    الهدية:
                                </span>
                                <span class="detail-value"><?php echo e($campaign->gift->name ?? 'غير محدد'); ?></span>
                            </div>
                            
                            <div class="detail-item">
                                <span class="detail-label">
                                    <i class="fas fa-users me-1"></i>
                                    العملاء المستهدفين:
                                </span>
                                <span class="detail-value"><?php echo e($campaign->target_client_count ?? 0); ?></span>
                            </div>
                            
                            <div class="detail-item">
                                <span class="detail-label">
                                    <i class="fas fa-envelope me-1"></i>
                                    عدد الرسائل:
                                </span>
                                <span class="detail-value"><?php echo e($campaign->message_count ?? 0); ?></span>
                            </div>
                            
                            <div class="detail-item">
                                <span class="detail-label">
                                    <i class="fas fa-money-bill-wave me-1"></i>
                                    التكلفة:
                                </span>
                                <span class="detail-value"><?php echo e(number_format($campaign->total_cost ?? 0, 2)); ?> ريال</span>
                            </div>
                            
                            <?php if($campaign->stage == 'completed'): ?>
                                <div class="progress-section mt-3">
                                    <div class="d-flex justify-content-between mb-1">
                                        <small>معدل التسليم</small>
                                        <small><?php echo e($campaign->sent_count ?? 0); ?>/<?php echo e($campaign->message_count ?? 0); ?></small>
                                    </div>
                                    <div class="progress">
                                        <div class="progress-bar bg-success" 
                                             style="width: <?php echo e($campaign->message_count > 0 ? (($campaign->sent_count ?? 0) / $campaign->message_count) * 100 : 0); ?>%">
                                        </div>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <div class="card-footer">
                        <div class="campaign-actions">
                            <?php if(in_array($campaign->stage, ['template_selection', 'client_filtering', 'message_count', 'platform_selection'])): ?>
                                <a href="<?php echo e(route('vendor.campaigns.wizard', $campaign)); ?>" class="btn btn-primary btn-sm">
                                    <i class="fas fa-arrow-right me-1"></i>
                                    متابعة الإنشاء
                                </a>
                            <?php endif; ?>
                            
                            <?php if($campaign->stage == 'completed'): ?>
                                <a href="<?php echo e(route('vendor.campaigns.deliveries', $campaign)); ?>" class="btn btn-info btn-sm">
                                    <i class="fas fa-truck me-1"></i>
                                    التسليمات
                                </a>
                                
                                <a href="<?php echo e(route('vendor.campaigns.analytics', $campaign)); ?>" class="btn btn-secondary btn-sm">
                                    <i class="fas fa-chart-bar me-1"></i>
                                    التحليلات
                                </a>
                            <?php endif; ?>
                            
                            <a href="<?php echo e(route('vendor.campaigns.show', $campaign)); ?>" class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-eye me-1"></i>
                                عرض
                            </a>
                        </div>
                        
                        <div class="campaign-date">
                            <small class="text-muted">
                                <i class="fas fa-calendar me-1"></i>
                                <?php echo e($campaign->created_at->diffForHumans()); ?>

                            </small>
                        </div>
                    </div>
                </div>
            </div>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
            <div class="col-12">
                <div class="empty-state">
                    <div class="empty-icon">
                        <i class="fas fa-bullhorn"></i>
                    </div>
                    <h3 class="empty-title">لا توجد حملات إعلانية حتى الآن</h3>
                    <p class="empty-description">ابدأ بإنشاء حملتك الأولى لاستهداف العملاء وتوزيع الهدايا</p>
                    <a href="<?php echo e(route('vendor.campaigns.create')); ?>" class="btn btn-primary btn-lg">
                        <i class="fas fa-plus me-2"></i>
                        إنشاء حملة جديدة
                    </a>
                </div>
            </div>
        <?php endif; ?>
    </div>

    <!-- Pagination -->
    <?php if($campaigns->hasPages()): ?>
        <div class="row">
            <div class="col-12">
                <div class="pagination-wrapper">
                    <?php echo e($campaigns->links()); ?>

                </div>
            </div>
        </div>
    <?php endif; ?>
</div>

<style>
.page-header {
    margin-bottom: 2rem;
    text-align: center;
}

.page-title {
    color: #333;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.page-subtitle {
    color: #6c757d;
    font-size: 1.1rem;
}

.stat-card {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    border-left: 4px solid;
    transition: all 0.3s ease;
    height: 100%;
    display: flex;
    align-items: center;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.stat-card.total {
    border-left-color: #667eea;
}

.stat-card.pending {
    border-left-color: #ffc107;
}

.stat-card.progress {
    border-left-color: #17a2b8;
}

.stat-card.completed {
    border-left-color: #28a745;
}

.stat-icon {
    font-size: 2.5rem;
    margin-right: 1rem;
    opacity: 0.8;
}

.stat-card.total .stat-icon {
    color: #667eea;
}

.stat-card.pending .stat-icon {
    color: #ffc107;
}

.stat-card.progress .stat-icon {
    color: #17a2b8;
}

.stat-card.completed .stat-icon {
    color: #28a745;
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: #333;
    margin-bottom: 0.25rem;
}

.stat-label {
    color: #6c757d;
    font-weight: 500;
}

.action-bar {
    background: white;
    padding: 1.5rem;
    border-radius: 15px;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
}

.campaign-item {
    border: none;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    height: 100%;
}

.campaign-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.campaign-item .card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 15px 15px 0 0;
}

.campaign-name {
    font-weight: 600;
    margin-bottom: 0.25rem;
    color: white;
}

.campaign-type {
    color: rgba(255, 255, 255, 0.8);
    margin: 0;
    font-size: 0.875rem;
}

.campaign-details {
    margin-bottom: 1rem;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.75rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #f8f9fa;
}

.detail-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.detail-label {
    color: #495057;
    font-weight: 500;
    flex: 1;
}

.detail-value {
    color: #667eea;
    font-weight: 600;
}

.campaign-actions {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
    margin-bottom: 0.75rem;
}

.campaign-date {
    border-top: 1px solid #f8f9fa;
    padding-top: 0.75rem;
}

.empty-state {
    text-align: center;
    padding: 4rem 2rem;
}

.empty-icon {
    font-size: 4rem;
    color: #e9ecef;
    margin-bottom: 1.5rem;
}

.empty-title {
    color: #333;
    font-weight: 600;
    margin-bottom: 1rem;
}

.empty-description {
    color: #6c757d;
    font-size: 1.1rem;
    margin-bottom: 2rem;
}

.pagination-wrapper {
    display: flex;
    justify-content: center;
    margin-top: 2rem;
}

@media (max-width: 768px) {
    .campaign-actions {
        flex-direction: column;
    }
    
    .campaign-actions .btn {
        width: 100%;
    }
    
    .action-bar .d-flex {
        flex-direction: column;
        gap: 1rem;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const gridViewBtn = document.getElementById('grid-view');
    const listViewBtn = document.getElementById('list-view');
    const campaignsContainer = document.getElementById('campaigns-container');

    function toggleView() {
        if (listViewBtn.checked) {
            campaignsContainer.classList.add('list-view');
            document.querySelectorAll('.campaign-card').forEach(card => {
                card.className = 'col-12 mb-3 campaign-card';
            });
        } else {
            campaignsContainer.classList.remove('list-view');
            document.querySelectorAll('.campaign-card').forEach(card => {
                card.className = 'col-lg-6 col-xl-4 mb-4 campaign-card';
            });
        }
    }

    gridViewBtn.addEventListener('change', toggleView);
    listViewBtn.addEventListener('change', toggleView);
});
</script>

<?php $__env->stopSection(); ?> 
<?php echo $__env->make('layouts.vendor', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\GiftsSaudi-Laravel (1)\resources\views/vendor/campaigns/index.blade.php ENDPATH**/ ?>