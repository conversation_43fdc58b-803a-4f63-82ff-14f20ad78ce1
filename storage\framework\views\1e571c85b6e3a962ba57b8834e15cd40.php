

<?php $__env->startSection('page-title', 'الإحصائيات'); ?>

<?php $__env->startSection('content'); ?>
<!-- Summary Cards -->
<div class="stats-summary mb-4">
    <div class="row">
        <div class="col-6 mb-3">
            <div class="stat-card">
                <div class="stat-number"><?php echo e($stats['today']); ?></div>
                <div class="stat-label">اليوم</div>
                <div class="stat-icon">
                    <i class="fas fa-calendar-day text-success"></i>
                </div>
            </div>
        </div>
        <div class="col-6 mb-3">
            <div class="stat-card info">
                <div class="stat-number"><?php echo e($stats['this_week']); ?></div>
                <div class="stat-label">هذا الأسبوع</div>
                <div class="stat-icon">
                    <i class="fas fa-calendar-week text-info"></i>
                </div>
            </div>
        </div>
        <div class="col-6 mb-3">
            <div class="stat-card warning">
                <div class="stat-number"><?php echo e($stats['this_month']); ?></div>
                <div class="stat-label">هذا الشهر</div>
                <div class="stat-icon">
                    <i class="fas fa-calendar-alt text-warning"></i>
                </div>
            </div>
        </div>
        <div class="col-6 mb-3">
            <div class="stat-card">
                <div class="stat-number"><?php echo e($stats['total']); ?></div>
                <div class="stat-label">الإجمالي</div>
                <div class="stat-icon">
                    <i class="fas fa-trophy text-success"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Pending Deliveries Alert -->
<?php if($stats['pending'] > 0): ?>
<div class="pending-alert mb-4">
    <div class="alert-mobile" style="background: rgba(255, 193, 7, 0.1); border-left: 4px solid #ffc107; color: #856404;">
        <div class="d-flex align-items-center">
            <i class="fas fa-exclamation-triangle me-3" style="font-size: 1.5rem;"></i>
            <div>
                <h6 class="mb-1">انتباه!</h6>
                <p class="mb-0">لديك <?php echo e($stats['pending']); ?> <?php echo e($stats['pending'] === 1 ? 'تسليم معلق' : 'تسليمات معلقة'); ?></p>
            </div>
        </div>
        <div class="mt-3">
            <a href="<?php echo e(route('employee.search')); ?>" class="btn btn-warning btn-sm">
                <i class="fas fa-search me-2"></i>
                ابدأ التسليم
            </a>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Performance Chart -->
<div class="chart-section mb-4">
    <div class="chart-card">
        <h6 class="chart-title mb-3">
            <i class="fas fa-chart-line me-2 text-info"></i>
            أداء آخر 7 أيام
        </h6>
        <div class="chart-container">
            <canvas id="performanceChart" width="400" height="200"></canvas>
        </div>
    </div>
</div>

<!-- Performance Metrics -->
<div class="metrics-section mb-4">
    <h6 class="metrics-title mb-3">
        <i class="fas fa-tachometer-alt me-2 text-warning"></i>
        مؤشرات الأداء
    </h6>
    
    <div class="metrics-grid">
        <!-- Daily Average -->
        <div class="metric-card">
            <div class="metric-icon">
                <i class="fas fa-calendar-day text-primary"></i>
            </div>
            <div class="metric-content">
                <div class="metric-value">
                    <?php echo e($stats['total'] > 0 ? round($stats['total'] / max(1, now()->diffInDays((Auth::guard('employee')->user()->created_at ?? Auth::user()->created_at ?? now()))), 1) : 0); ?>

                </div>
                <div class="metric-label">متوسط يومي</div>
            </div>
        </div>
        
        <!-- Weekly Average -->
        <div class="metric-card">
            <div class="metric-icon">
                <i class="fas fa-calendar-week text-success"></i>
            </div>
            <div class="metric-content">
                <div class="metric-value">
                    <?php echo e($stats['total'] > 0 ? round($stats['total'] / max(1, ceil(now()->diffInDays((Auth::guard('employee')->user()->created_at ?? Auth::user()->created_at ?? now())) / 7)), 1) : 0); ?>

                </div>
                <div class="metric-label">متوسط أسبوعي</div>
            </div>
        </div>
        
        <!-- This Week Progress -->
        <div class="metric-card">
            <div class="metric-icon">
                <i class="fas fa-percentage text-warning"></i>
            </div>
            <div class="metric-content">
                <div class="metric-value">
                    <?php echo e($stats['this_week'] > 0 ? round(($stats['this_week'] / max($stats['this_week'], 10)) * 100) : 0); ?>%
                </div>
                <div class="metric-label">تقدم الأسبوع</div>
            </div>
        </div>
        
        <!-- Monthly Target -->
        <div class="metric-card">
            <div class="metric-icon">
                <i class="fas fa-bullseye text-danger"></i>
            </div>
            <div class="metric-content">
                <div class="metric-value">
                    <?php echo e($stats['this_month'] > 0 ? round(($stats['this_month'] / max($stats['this_month'], 20)) * 100) : 0); ?>%
                </div>
                <div class="metric-label">هدف الشهر</div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Activity -->
<div class="activity-section mb-4">
    <h6 class="activity-title mb-3">
        <i class="fas fa-history me-2 text-secondary"></i>
        النشاط الحديث
    </h6>
    
    <div class="activity-timeline">
        <?php if($stats['today'] > 0): ?>
        <div class="timeline-item">
            <div class="timeline-marker success">
                <i class="fas fa-check"></i>
            </div>
            <div class="timeline-content">
                <div class="timeline-title">تسليمات اليوم</div>
                <div class="timeline-description">تم تسليم <?php echo e($stats['today']); ?> <?php echo e($stats['today'] === 1 ? 'هدية' : 'هدايا'); ?> اليوم</div>
                <div class="timeline-time"><?php echo e(now()->format('H:i')); ?></div>
            </div>
        </div>
        <?php endif; ?>
        
        <?php if($stats['this_week'] > 0): ?>
        <div class="timeline-item">
            <div class="timeline-marker info">
                <i class="fas fa-calendar-week"></i>
            </div>
            <div class="timeline-content">
                <div class="timeline-title">أداء الأسبوع</div>
                <div class="timeline-description"><?php echo e($stats['this_week']); ?> تسليم هذا الأسبوع</div>
                <div class="timeline-time"><?php echo e(now()->startOfWeek()->format('M d')); ?> - <?php echo e(now()->endOfWeek()->format('M d')); ?></div>
            </div>
        </div>
        <?php endif; ?>
        
        <?php if($stats['pending'] > 0): ?>
        <div class="timeline-item">
            <div class="timeline-marker warning">
                <i class="fas fa-clock"></i>
            </div>
            <div class="timeline-content">
                <div class="timeline-title">تسليمات معلقة</div>
                <div class="timeline-description"><?php echo e($stats['pending']); ?> <?php echo e($stats['pending'] === 1 ? 'تسليم معلق' : 'تسليمات معلقة'); ?></div>
                <div class="timeline-time">قيد الانتظار</div>
            </div>
        </div>
        <?php endif; ?>
        
        <?php if($stats['total'] === 0): ?>
        <div class="timeline-item">
            <div class="timeline-marker muted">
                <i class="fas fa-info-circle"></i>
            </div>
            <div class="timeline-content">
                <div class="timeline-title">لا يوجد نشاط</div>
                <div class="timeline-description">لم يتم تسجيل أي تسليمات بعد</div>
                <div class="timeline-time">ابدأ العمل</div>
            </div>
        </div>
        <?php endif; ?>
    </div>
</div>

<!-- Quick Actions -->
<div class="quick-actions-section">
    <h6 class="actions-title mb-3">
        <i class="fas fa-bolt me-2 text-warning"></i>
        إجراءات سريعة
    </h6>
    
    <div class="actions-grid">
        <a href="<?php echo e(route('employee.search')); ?>" class="action-card">
            <div class="action-icon">
                <i class="fas fa-search text-success"></i>
            </div>
            <div class="action-content">
                <div class="action-title">بحث جديد</div>
                <div class="action-description">ابحث عن عميل للتسليم</div>
            </div>
        </a>
        
        <a href="<?php echo e(route('employee.dashboard')); ?>" class="action-card">
            <div class="action-icon">
                <i class="fas fa-home text-primary"></i>
            </div>
            <div class="action-content">
                <div class="action-title">الرئيسية</div>
                <div class="action-description">العودة للوحة الرئيسية</div>
            </div>
        </a>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<style>
.chart-card {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    border: 1px solid #e9ecef;
}

.chart-title, .metrics-title, .activity-title, .actions-title {
    color: #343a40;
    font-weight: 600;
    margin-bottom: 1rem;
}

.chart-container {
    position: relative;
    height: 200px;
    width: 100%;
}

.metrics-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
    margin-bottom: 1rem;
}

.metric-card {
    background: white;
    border-radius: 12px;
    padding: 1rem;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    gap: 0.75rem;
    border: 1px solid #f8f9fa;
    transition: all 0.3s ease;
}

.metric-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
}

.metric-icon {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    background: rgba(0, 123, 255, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.1rem;
    flex-shrink: 0;
}

.metric-value {
    font-size: 1.25rem;
    font-weight: 700;
    color: #343a40;
    line-height: 1;
}

.metric-label {
    font-size: 0.8rem;
    color: #6c757d;
    margin-top: 0.25rem;
}

.activity-timeline {
    position: relative;
    padding: 0;
}

.timeline-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    margin-bottom: 1.5rem;
    position: relative;
}

.timeline-item:not(:last-child):before {
    content: '';
    position: absolute;
    left: 15px;
    top: 30px;
    bottom: -15px;
    width: 2px;
    background: #e9ecef;
    z-index: 1;
}

.timeline-marker {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
    color: white;
    flex-shrink: 0;
    z-index: 2;
    position: relative;
}

.timeline-marker.success { background: #28a745; }
.timeline-marker.info { background: #17a2b8; }
.timeline-marker.warning { background: #ffc107; color: #856404; }
.timeline-marker.muted { background: #6c757d; }

.timeline-content {
    flex: 1;
    background: white;
    border-radius: 10px;
    padding: 1rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border: 1px solid #f8f9fa;
}

.timeline-title {
    font-weight: 600;
    color: #343a40;
    margin-bottom: 0.25rem;
}

.timeline-description {
    color: #6c757d;
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
}

.timeline-time {
    font-size: 0.75rem;
    color: #adb5bd;
}

.actions-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
}

.action-card {
    background: white;
    border-radius: 12px;
    padding: 1rem;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    gap: 0.75rem;
    text-decoration: none;
    color: inherit;
    border: 1px solid #f8f9fa;
    transition: all 0.3s ease;
}

.action-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
    color: inherit;
    text-decoration: none;
}

.action-icon {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    background: rgba(40, 167, 69, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.1rem;
    flex-shrink: 0;
}

.action-title {
    font-weight: 600;
    color: #343a40;
    font-size: 0.9rem;
}

.action-description {
    font-size: 0.75rem;
    color: #6c757d;
    margin-top: 0.25rem;
}

/* Mobile optimizations */
@media (max-width: 576px) {
    .metrics-grid, .actions-grid {
        grid-template-columns: 1fr;
    }
    
    .chart-card {
        padding: 1rem;
    }
    
    .metric-card, .action-card {
        padding: 0.75rem;
    }
    
    .timeline-item {
        gap: 0.75rem;
    }
    
    .timeline-content {
        padding: 0.75rem;
    }
    
    .metric-icon, .action-icon {
        width: 35px;
        height: 35px;
        font-size: 1rem;
    }
}

/* Loading animation for chart */
.chart-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 200px;
    color: #6c757d;
}
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
// Chart data from backend
const chartData = <?php echo json_encode($chartData, 15, 512) ?>;

// Create performance chart
document.addEventListener('DOMContentLoaded', function() {
    const ctx = document.getElementById('performanceChart').getContext('2d');
    
    new Chart(ctx, {
        type: 'line',
        data: {
            labels: chartData.map(item => item.date),
            datasets: [{
                label: 'التسليمات',
                data: chartData.map(item => item.count),
                borderColor: '#28a745',
                backgroundColor: 'rgba(40, 167, 69, 0.1)',
                borderWidth: 3,
                fill: true,
                tension: 0.4,
                pointBackgroundColor: '#28a745',
                pointBorderColor: '#fff',
                pointBorderWidth: 2,
                pointRadius: 6,
                pointHoverRadius: 8
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        stepSize: 1,
                        color: '#6c757d',
                        font: {
                            family: 'Cairo',
                            size: 12
                        }
                    },
                    grid: {
                        color: 'rgba(0, 0, 0, 0.05)'
                    }
                },
                x: {
                    ticks: {
                        color: '#6c757d',
                        font: {
                            family: 'Cairo',
                            size: 12
                        }
                    },
                    grid: {
                        display: false
                    }
                }
            },
            animation: {
                duration: 2000,
                easing: 'easeInOutQuart'
            }
        }
    });
});

// Add some animations
document.addEventListener('DOMContentLoaded', function() {
    // Animate metric cards
    const metricCards = document.querySelectorAll('.metric-card');
    metricCards.forEach((card, index) => {
        setTimeout(() => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(20px)';
            card.style.transition = 'all 0.5s ease';
            
            setTimeout(() => {
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }, 100);
        }, index * 150);
    });
    
    // Animate timeline items
    const timelineItems = document.querySelectorAll('.timeline-item');
    timelineItems.forEach((item, index) => {
        setTimeout(() => {
            item.style.opacity = '0';
            item.style.transform = 'translateX(30px)';
            item.style.transition = 'all 0.6s ease';
            
            setTimeout(() => {
                item.style.opacity = '1';
                item.style.transform = 'translateX(0)';
            }, 100);
        }, (index * 200) + 1000);
    });
});
</script>
<?php $__env->stopPush(); ?> 
<?php echo $__env->make('layouts.employee', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\GiftsSaudi-Laravel (1)\resources\views\employee\statistics.blade.php ENDPATH**/ ?>