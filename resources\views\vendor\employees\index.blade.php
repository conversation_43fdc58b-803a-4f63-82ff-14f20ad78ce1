@extends('layouts.vendor')

@section('title', 'إدارة الموظفين')

@section('content')
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-1">
                <i class="fas fa-users me-2"></i>
                إدارة الموظفين
            </h1>
            <p class="text-muted">أضف وادير موظفيك المسؤولين عن تسليم الهدايا</p>
        </div>
        <a href="{{ route('vendor.employees.create') }}" class="btn btn-primary">
            <i class="fas fa-plus me-2"></i>
            إضافة موظف جديد
        </a>
    </div>

    <!-- Stats Row -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-primary text-white">
                <div class="card-body text-center">
                    <h3>{{ $employees->count() }}</h3>
                    <p class="mb-0">إجمالي الموظفين</p>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-success text-white">
                <div class="card-body text-center">
                    <h3>{{ $employees->where('status', 'active')->count() }}</h3>
                    <p class="mb-0">نشط</p>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-warning text-white">
                <div class="card-body text-center">
                    <h3>{{ $employees->where('status', 'pending')->count() }}</h3>
                    <p class="mb-0">في انتظار الموافقة</p>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-danger text-white">
                <div class="card-body text-center">
                    <h3>{{ $employees->where('status', 'inactive')->count() }}</h3>
                    <p class="mb-0">غير نشط</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Employees Table -->
    <div class="card">
        <div class="card-header">
            <h5>قائمة الموظفين</h5>
        </div>
        <div class="card-body">
            @if($employees->count() > 0)
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>الصورة</th>
                                <th>اسم الموظف</th>
                                <th>رقم الهاتف</th>
                                <th>البريد الإلكتروني</th>
                                <th>المنصب</th>
                                <th>الحالة</th>
                                <th>التسليمات</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($employees as $index => $employee)
                                <tr>
                                    <td>{{ $index + 1 }}</td>
                                    <td>
                                        @if($employee->avatar)
                                            <img src="{{ asset('storage/' . $employee->avatar) }}" 
                                                 alt="{{ $employee->name }}" 
                                                 class="rounded-circle" 
                                                 width="40" 
                                                 height="40">
                                        @else
                                            <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center text-white" 
                                                 style="width: 40px; height: 40px;">
                                                {{ substr($employee->name, 0, 1) }}
                                            </div>
                                        @endif
                                    </td>
                                    <td>
                                        <strong>{{ $employee->name }}</strong>
                                        @if($employee->national_id)
                                            <br><small class="text-muted">هوية: {{ $employee->national_id }}</small>
                                        @endif
                                    </td>
                                    <td>
                                        <a href="tel:{{ $employee->phone }}" class="text-decoration-none">
                                            {{ $employee->phone }}
                                        </a>
                                    </td>
                                    <td>
                                        @if($employee->email)
                                            <a href="mailto:{{ $employee->email }}" class="text-decoration-none">
                                                {{ $employee->email }}
                                            </a>
                                        @else
                                            <span class="text-muted">غير محدد</span>
                                        @endif
                                    </td>
                                    <td>{{ $employee->position ?? 'موظف تسليم' }}</td>
                                    <td>
                                        @switch($employee->status)
                                            @case('active')
                                                <span class="badge bg-success">
                                                    <i class="fas fa-check-circle"></i> نشط
                                                </span>
                                                @break
                                            @case('pending')
                                                <span class="badge bg-warning">
                                                    <i class="fas fa-clock"></i> في انتظار الموافقة
                                                </span>
                                                @break
                                            @default
                                                <span class="badge bg-danger">
                                                    <i class="fas fa-times-circle"></i> غير نشط
                                                </span>
                                        @endswitch
                                    </td>
                                    <td>
                                        <div class="text-center">
                                            <strong>{{ $employee->deliveries->count() }}</strong>
                                            <br><small class="text-muted">إجمالي</small>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="{{ route('vendor.employees.show', $employee) }}" 
                                               class="btn btn-outline-info">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{{ route('vendor.employees.edit', $employee) }}" 
                                               class="btn btn-outline-warning">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            @if($employee->status === 'inactive')
                                                <button onclick="activateEmployee({{ $employee->id }})" 
                                                        class="btn btn-outline-success">
                                                    <i class="fas fa-play"></i>
                                                </button>
                                            @else
                                                <button onclick="deactivateEmployee({{ $employee->id }})" 
                                                        class="btn btn-outline-danger">
                                                    <i class="fas fa-pause"></i>
                                                </button>
                                            @endif
                                            <a href="https://wa.me/{{ preg_replace('/[^0-9]/', '', $employee->phone) }}" 
                                               target="_blank" 
                                               class="btn btn-outline-success">
                                                <i class="fab fa-whatsapp"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            @else
                <div class="text-center py-4">
                    <i class="fas fa-users fa-3x text-muted mb-3"></i>
                    <h5>لا يوجد موظفين حتى الآن</h5>
                    <p class="text-muted">ابدأ بإضافة موظفيك لمساعدتك في تسليم الهدايا</p>
                    <a href="{{ route('vendor.employees.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>
                        إضافة موظف جديد
                    </a>
                </div>
            @endif
        </div>
    </div>

    <!-- Performance Overview -->
    @if($employees->where('status', 'active')->count() > 0)
        <div class="card mt-4">
            <div class="card-header">
                <h5>أداء الموظفين (آخر 30 يوم)</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    @foreach($employees->where('status', 'active')->take(4) as $employee)
                        @php
                            $recentDeliveries = $employee->deliveries()->where('created_at', '>=', now()->subDays(30))->count();
                            $successfulDeliveries = $employee->deliveries()->where('status', 'delivered')->where('created_at', '>=', now()->subDays(30))->count();
                            $successRate = $recentDeliveries > 0 ? ($successfulDeliveries / $recentDeliveries) * 100 : 0;
                        @endphp
                        <div class="col-lg-3 col-md-6 mb-3">
                            <div class="card border-0 bg-light">
                                <div class="card-body text-center">
                                    @if($employee->avatar)
                                        <img src="{{ asset('storage/' . $employee->avatar) }}" 
                                             alt="{{ $employee->name }}" 
                                             class="rounded-circle mb-2" 
                                             width="60" 
                                             height="60">
                                    @else
                                        <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center text-white mx-auto mb-2" 
                                             style="width: 60px; height: 60px; font-size: 1.5rem;">
                                            {{ substr($employee->name, 0, 1) }}
                                        </div>
                                    @endif
                                    <h6 class="mb-1">{{ $employee->name }}</h6>
                                    <div class="row text-center">
                                        <div class="col">
                                            <small class="text-muted">التسليمات</small>
                                            <div class="fw-bold">{{ $recentDeliveries }}</div>
                                        </div>
                                        <div class="col">
                                            <small class="text-muted">معدل النجاح</small>
                                            <div class="fw-bold text-success">{{ number_format($successRate, 1) }}%</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        </div>
    @endif
</div>
@endsection

@push('scripts')
<script>
function activateEmployee(employeeId) {
    if (confirm('هل تريد تفعيل هذا الموظف؟')) {
        fetch(`/vendor/employees/${employeeId}/update-status`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({ status: 'active' })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('حدث خطأ في تفعيل الموظف');
            }
        })
        .catch(error => {
            alert('حدث خطأ في الاتصال');
        });
    }
}

function deactivateEmployee(employeeId) {
    if (confirm('هل تريد إلغاء تفعيل هذا الموظف؟')) {
        fetch(`/vendor/employees/${employeeId}/update-status`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({ status: 'inactive' })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('حدث خطأ في إلغاء تفعيل الموظف');
            }
        })
        .catch(error => {
            alert('حدث خطأ في الاتصال');
        });
    }
}
</script>
@endpush 