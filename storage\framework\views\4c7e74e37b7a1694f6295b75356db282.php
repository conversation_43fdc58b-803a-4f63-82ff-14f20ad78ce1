

<?php $__env->startSection('title', 'تفاصيل الدفعة'); ?>

<?php $__env->startSection('content'); ?>
<style>
.payment-details-card {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    border: 1px solid #ff6b35;
    border-radius: 15px;
    padding: 30px;
    margin-bottom: 30px;
    box-shadow: 0 10px 30px rgba(255, 107, 53, 0.1);
}

.payment-header {
    text-align: center;
    padding: 30px 0;
    border-bottom: 2px solid #333;
    margin-bottom: 30px;
}

.payment-id-large {
    font-size: 2.5rem;
    font-weight: bold;
    color: #ff6b35;
    margin-bottom: 10px;
}

.payment-amount-large {
    font-size: 3rem;
    font-weight: bold;
    color: #fff;
    margin: 20px 0;
}

.payment-status-large {
    padding: 10px 30px;
    border-radius: 30px;
    font-size: 1.2rem;
    font-weight: 600;
    text-transform: uppercase;
    display: inline-block;
    margin-bottom: 20px;
}

.status-paid { background: rgba(40, 167, 69, 0.3); color: #28a745; border: 2px solid #28a745; }
.status-pending { background: rgba(255, 193, 7, 0.3); color: #ffc107; border: 2px solid #ffc107; }
.status-failed { background: rgba(220, 53, 69, 0.3); color: #dc3545; border: 2px solid #dc3545; }
.status-expired { background: rgba(108, 117, 125, 0.3); color: #6c757d; border: 2px solid #6c757d; }
.status-cancelled { background: rgba(52, 58, 64, 0.3); color: #343a40; border: 2px solid #343a40; }
.status-initiated { background: rgba(23, 162, 184, 0.3); color: #17a2b8; border: 2px solid #17a2b8; }

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-bottom: 30px;
}

.info-section {
    background: rgba(42, 42, 42, 0.5);
    border-radius: 15px;
    padding: 25px;
    border: 1px solid #555;
}

.info-section h5 {
    color: #ff6b35;
    margin-bottom: 20px;
    font-weight: bold;
    display: flex;
    align-items: center;
    gap: 10px;
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #333;
}

.info-item:last-child {
    border-bottom: none;
}

.info-label {
    color: #ccc;
    font-weight: 600;
    flex: 1;
}

.info-value {
    color: #fff;
    font-weight: 600;
    text-align: right;
    flex: 1;
}

.payment-method-badge {
    background: linear-gradient(45deg, #ff6b35, #f7931e);
    color: white;
    padding: 5px 15px;
    border-radius: 15px;
    font-size: 0.9rem;
    font-weight: 600;
}

.metadata-section {
    background: rgba(42, 42, 42, 0.3);
    border-radius: 10px;
    padding: 20px;
    margin-top: 20px;
}

.metadata-item {
    display: flex;
    justify-content: space-between;
    padding: 8px 0;
    border-bottom: 1px solid #555;
}

.metadata-item:last-child {
    border-bottom: none;
}

.btn-gradient {
    background: linear-gradient(45deg, #ff6b35, #f7931e);
    border: none;
    color: white;
    padding: 12px 25px;
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-gradient:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(255, 107, 53, 0.3);
    color: white;
}

.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #ff6b35;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
    background: rgba(42, 42, 42, 0.5);
    padding: 20px;
    border-radius: 10px;
    border-left: 4px solid #ff6b35;
}

.timeline-item::before {
    content: '';
    position: absolute;
    left: -36px;
    top: 25px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #ff6b35;
    border: 3px solid #1a1a1a;
}

.print-section {
    text-align: center;
    margin-top: 30px;
    padding: 20px;
    background: rgba(255, 107, 53, 0.1);
    border-radius: 10px;
    border: 1px solid rgba(255, 107, 53, 0.3);
}

@media print {
    body * {
        visibility: hidden;
    }
    .print-area, .print-area * {
        visibility: visible;
    }
    .print-area {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
    }
    .btn, .page-actions, .breadcrumb, .no-print {
        display: none !important;
    }
}
</style>

<div class="page-header">
    <h1 class="page-title">
        <i class="fas fa-credit-card me-2"></i>
        تفاصيل الدفعة
    </h1>
    <div class="page-actions no-print">
        <a href="<?php echo e(route('admin.payments.index')); ?>" class="btn btn-outline-secondary me-2">
            <i class="fas fa-arrow-right me-2"></i>
            العودة للمدفوعات
        </a>
        <button onclick="window.print()" class="btn btn-outline-info me-2">
            <i class="fas fa-print me-2"></i>
            طباعة
        </button>
        <?php if(!$payment->isPaid()): ?>
            <button onclick="updatePaymentStatus('PAID')" class="btn btn-gradient">
                <i class="fas fa-check me-2"></i>
                تأكيد الدفع
            </button>
        <?php endif; ?>
    </div>
</div>

<div class="print-area">
    <!-- Payment Header -->
    <div class="payment-details-card">
        <div class="payment-header">
            <div class="payment-id-large"><?php echo e($payment->payment_id); ?></div>
            <div class="text-muted">رقم المرجع: <?php echo e($payment->order_reference); ?></div>
            <div class="payment-amount-large"><?php echo e($payment->formatted_amount); ?></div>
            <div class="payment-status-large status-<?php echo e(strtolower($payment->payment_status)); ?>">
                <?php echo e($payment->status_display_name); ?>

            </div>
            <div class="text-muted"><?php echo e($payment->created_at->format('Y/m/d - H:i:s')); ?></div>
        </div>

        <!-- Payment Information Grid -->
        <div class="info-grid">
            <!-- Customer Information -->
            <div class="info-section">
                <h5>
                    <i class="fas fa-user"></i>
                    معلومات العميل
                </h5>
                <div class="info-item">
                    <span class="info-label">الاسم</span>
                    <span class="info-value"><?php echo e($payment->customer_name); ?></span>
                </div>
                <div class="info-item">
                    <span class="info-label">البريد الإلكتروني</span>
                    <span class="info-value"><?php echo e($payment->customer_email); ?></span>
                </div>
                <div class="info-item">
                    <span class="info-label">رقم الهاتف</span>
                    <span class="info-value"><?php echo e($payment->customer_phone); ?></span>
                </div>
            </div>

            <!-- Payment Information -->
            <div class="info-section">
                <h5>
                    <i class="fas fa-credit-card"></i>
                    معلومات الدفع
                </h5>
                <div class="info-item">
                    <span class="info-label">المبلغ</span>
                    <span class="info-value"><?php echo e($payment->formatted_amount); ?></span>
                </div>
                <div class="info-item">
                    <span class="info-label">طريقة الدفع</span>
                    <span class="info-value">
                        <?php if($payment->payment_method): ?>
                            <span class="payment-method-badge"><?php echo e($payment->payment_method); ?></span>
                        <?php else: ?>
                            <span class="text-muted">غير محدد</span>
                        <?php endif; ?>
                    </span>
                </div>
                <div class="info-item">
                    <span class="info-label">رقم المعاملة</span>
                    <span class="info-value"><?php echo e($payment->transaction_id ?? 'غير محدد'); ?></span>
                </div>
                <div class="info-item">
                    <span class="info-label">وضع الاختبار</span>
                    <span class="info-value">
                        <?php if($payment->test_mode): ?>
                            <span class="badge bg-warning">اختبار</span>
                        <?php else: ?>
                            <span class="badge bg-success">إنتاج</span>
                        <?php endif; ?>
                    </span>
                </div>
            </div>

            <!-- Timeline -->
            <div class="info-section">
                <h5>
                    <i class="fas fa-clock"></i>
                    التواريخ المهمة
                </h5>
                <div class="info-item">
                    <span class="info-label">تاريخ الإنشاء</span>
                    <span class="info-value"><?php echo e($payment->created_at->format('Y/m/d H:i:s')); ?></span>
                </div>
                <div class="info-item">
                    <span class="info-label">آخر تحديث</span>
                    <span class="info-value"><?php echo e($payment->updated_at->format('Y/m/d H:i:s')); ?></span>
                </div>
                <div class="info-item">
                    <span class="info-label">منذ</span>
                    <span class="info-value"><?php echo e($payment->created_at->diffForHumans()); ?></span>
                </div>
            </div>

            <!-- Status History -->
            <div class="info-section">
                <h5>
                    <i class="fas fa-history"></i>
                    إجراءات سريعة
                </h5>
                <div class="d-grid gap-2">
                    <?php if(!$payment->isPaid()): ?>
                        <button onclick="updatePaymentStatus('PAID')" class="btn btn-success btn-sm">
                            <i class="fas fa-check me-2"></i>
                            تأكيد الدفع
                        </button>
                    <?php endif; ?>
                    
                    <?php if($payment->isPending()): ?>
                        <button onclick="updatePaymentStatus('FAILED')" class="btn btn-danger btn-sm">
                            <i class="fas fa-times me-2"></i>
                            رفض الدفع
                        </button>
                    <?php endif; ?>
                    
                    <?php if(!$payment->isPaid()): ?>
                        <button onclick="updatePaymentStatus('CANCELLED')" class="btn btn-secondary btn-sm">
                            <i class="fas fa-ban me-2"></i>
                            إلغاء الدفع
                        </button>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Metadata Section -->
        <?php if($payment->metadata): ?>
            <div class="info-section">
                <h5>
                    <i class="fas fa-info-circle"></i>
                    بيانات إضافية
                </h5>
                <div class="metadata-section">
                    <?php $__currentLoopData = $payment->metadata; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="metadata-item">
                            <span class="info-label"><?php echo e(ucfirst(str_replace('_', ' ', $key))); ?></span>
                            <span class="info-value">
                                <?php if(is_array($value)): ?>
                                    <?php echo e(json_encode($value, JSON_UNESCAPED_UNICODE)); ?>

                                <?php else: ?>
                                    <?php echo e($value); ?>

                                <?php endif; ?>
                            </span>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            </div>
        <?php endif; ?>
    </div>

    <!-- Print Section -->
    <div class="print-section no-print">
        <h5 class="text-white mb-3">
            <i class="fas fa-download me-2"></i>
            خيارات التصدير
        </h5>
        <div class="d-flex justify-content-center gap-3">
            <button onclick="window.print()" class="btn btn-gradient">
                <i class="fas fa-print me-2"></i>
                طباعة التفاصيل
            </button>
            <a href="<?php echo e(route('admin.payments.index', ['search' => $payment->payment_id])); ?>" class="btn btn-outline-info">
                <i class="fas fa-search me-2"></i>
                البحث عن مدفوعات مشابهة
            </a>
        </div>
    </div>
</div>

<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
function updatePaymentStatus(status) {
    const statusNames = {
        'PAID': 'مدفوعة',
        'FAILED': 'فاشلة',
        'CANCELLED': 'ملغية',
        'EXPIRED': 'منتهية الصلاحية'
    };
    
    if (confirm(`هل أنت متأكد من تغيير حالة الدفع إلى "${statusNames[status]}"؟`)) {
        fetch(`<?php echo e(route('admin.payments.update-status', $payment)); ?>`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({ status: status })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('حدث خطأ أثناء تحديث حالة الدفع.');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ أثناء تحديث حالة الدفع.');
        });
    }
}

// Auto-refresh for pending payments
<?php if($payment->isPending()): ?>
    setInterval(function() {
        // Check if payment status has been updated
        fetch(`<?php echo e(route('admin.payments.show', $payment)); ?>`, {
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.text())
        .then(html => {
            // You could implement real-time status checking here
            // For now, we'll just refresh every 30 seconds for pending payments
        });
    }, 30000); // Check every 30 seconds
<?php endif; ?>
</script>
<?php $__env->stopPush(); ?> 
<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\GiftsSaudi-Laravel (1)\resources\views\admin\payments\show.blade.php ENDPATH**/ ?>