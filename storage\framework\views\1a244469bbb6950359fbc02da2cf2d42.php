

<?php $__env->startSection('title', 'إدارة الهدايا'); ?>

<?php $__env->startPush('styles'); ?>
<style>
    .gifts-container {
        background: transparent;
        min-height: 100vh;
        padding: 30px;
    }

    .page-header {
        background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
        border: 1px solid rgba(255, 107, 53, 0.2);
        border-radius: 25px;
        padding: 30px;
        margin-bottom: 30px;
        position: relative;
        overflow: hidden;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4);
    }

    .page-title {
        color: white;
        font-weight: 800;
        font-size: 2rem;
        margin-bottom: 10px;
        background: linear-gradient(135deg, #ffffff 0%, #ff6b35 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .page-subtitle {
        color: #b0b0b0;
        font-size: 1rem;
        margin-bottom: 0;
    }

    .action-buttons {
        display: flex;
        gap: 15px;
        align-items: center;
    }

    .btn-primary {
        background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
        border: none;
        border-radius: 15px;
        padding: 12px 25px;
        color: white;
        font-weight: 600;
        text-decoration: none;
        transition: all 0.3s ease;
        box-shadow: 0 8px 25px rgba(255, 107, 53, 0.3);
    }

    .btn-primary:hover {
        transform: translateY(-3px);
        box-shadow: 0 15px 35px rgba(255, 107, 53, 0.4);
        color: white;
    }

    .gifts-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
        gap: 25px;
    }

    .gift-card {
        background: linear-gradient(145deg, #2d2d2d, #1a1a1a);
        border: 1px solid rgba(255, 107, 53, 0.1);
        border-radius: 20px;
        padding: 25px;
        position: relative;
        overflow: hidden;
        transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
    }

    .gift-card:hover {
        transform: translateY(-10px) scale(1.02);
        box-shadow: 0 25px 50px rgba(0, 0, 0, 0.4);
        border-color: rgba(255, 107, 53, 0.3);
    }

    .gift-image {
        width: 100%;
        height: 200px;
        border-radius: 15px;
        object-fit: cover;
        margin-bottom: 20px;
        background: linear-gradient(135deg, rgba(255, 107, 53, 0.1), rgba(255, 107, 53, 0.05));
    }

    .gift-title {
        color: white;
        font-weight: 700;
        font-size: 1.2rem;
        margin-bottom: 10px;
    }

    .gift-description {
        color: #b0b0b0;
        font-size: 0.9rem;
        margin-bottom: 15px;
        line-height: 1.5;
    }

    .gift-meta {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
    }

    .gift-price {
        background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        font-weight: 800;
        font-size: 1.1rem;
    }

    .gift-stock {
        background: rgba(255, 107, 53, 0.1);
        border: 1px solid rgba(255, 107, 53, 0.3);
        color: #ff6b35;
        padding: 5px 12px;
        border-radius: 10px;
        font-size: 0.8rem;
        font-weight: 600;
    }

    .gift-status {
        position: absolute;
        top: 15px;
        right: 15px;
        padding: 8px 15px;
        border-radius: 12px;
        font-size: 0.8rem;
        font-weight: 600;
    }

    .status-pending {
        background: rgba(255, 193, 7, 0.2);
        color: #ffc107;
        border: 1px solid rgba(255, 193, 7, 0.3);
    }

    .status-approved {
        background: rgba(0, 212, 170, 0.2);
        color: #00d4aa;
        border: 1px solid rgba(0, 212, 170, 0.3);
    }

    .status-rejected {
        background: rgba(255, 71, 87, 0.2);
        color: #ff4757;
        border: 1px solid rgba(255, 71, 87, 0.3);
    }

    .gift-actions {
        display: flex;
        gap: 10px;
        justify-content: center;
    }

    .btn-sm {
        padding: 8px 15px;
        font-size: 0.8rem;
        border-radius: 10px;
        font-weight: 600;
        text-decoration: none;
        transition: all 0.3s ease;
    }

    .btn-success {
        background: #00d4aa;
        color: white;
        border: none;
    }

    .btn-warning {
        background: #ffc107;
        color: white;
        border: none;
    }

    .btn-danger {
        background: #ff4757;
        color: white;
        border: none;
    }

    .btn-sm:hover {
        transform: translateY(-2px);
        color: white;
    }

    .empty-state {
        text-align: center;
        padding: 60px 20px;
        background: linear-gradient(145deg, #2d2d2d, #1a1a1a);
        border: 1px solid rgba(255, 107, 53, 0.1);
        border-radius: 20px;
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
    }

    .empty-icon {
        font-size: 4rem;
        color: rgba(255, 107, 53, 0.3);
        margin-bottom: 20px;
    }

    .empty-title {
        color: white;
        font-size: 1.5rem;
        font-weight: 700;
        margin-bottom: 10px;
    }

    .empty-subtitle {
        color: #b0b0b0;
        margin-bottom: 25px;
    }

    @media (max-width: 768px) {
        .gifts-grid {
            grid-template-columns: 1fr;
        }
        
        .page-header {
            padding: 20px;
        }
        
        .action-buttons {
            flex-direction: column;
            align-items: stretch;
        }
    }
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
<div class="gifts-container">
    <!-- Page Header -->
    <div class="page-header">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <h1 class="page-title">
                    <i class="fas fa-gift me-3"></i>
                    إدارة الهدايا
                </h1>
                <p class="page-subtitle">
                    أضف وادير هداياك، وتابع حالة الموافقة عليها من الإدارة
                </p>
            </div>
            <div class="col-lg-4 text-end">
                <div class="action-buttons">
                    <a href="<?php echo e(route('vendor.gifts.create')); ?>" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>
                        إضافة هدية جديدة
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Gifts Grid -->
    <?php if($gifts->count() > 0): ?>
        <div class="gifts-grid">
            <?php $__currentLoopData = $gifts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $gift): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="gift-card">
                    <!-- Status Badge -->
                    <div class="gift-status status-<?php echo e($gift->approval_status ?? 'pending'); ?>">
                        <?php switch($gift->approval_status ?? 'pending'):
                            case ('approved'): ?>
                                <i class="fas fa-check-circle me-1"></i>
                                معتمدة
                                <?php break; ?>
                            <?php case ('rejected'): ?>
                                <i class="fas fa-times-circle me-1"></i>
                                مرفوضة
                                <?php break; ?>
                            <?php default: ?>
                                <i class="fas fa-clock me-1"></i>
                                في انتظار الموافقة
                        <?php endswitch; ?>
                    </div>

                    <!-- Gift Image -->
                    <?php if($gift->image): ?>
                        <img src="<?php echo e(asset('storage/' . $gift->image)); ?>" alt="<?php echo e($gift->name); ?>" class="gift-image">
                    <?php else: ?>
                        <div class="gift-image d-flex align-items-center justify-content-center">
                            <i class="fas fa-gift" style="font-size: 3rem; color: rgba(255, 107, 53, 0.3);"></i>
                        </div>
                    <?php endif; ?>

                    <!-- Gift Info -->
                    <h3 class="gift-title"><?php echo e($gift->name); ?></h3>
                    <p class="gift-description"><?php echo e(Str::limit($gift->description, 100)); ?></p>

                    <!-- Meta Info -->
                    <div class="gift-meta">
                        <span class="gift-price"><?php echo e(number_format($gift->price)); ?> ريال</span>
                        <span class="gift-stock">
                            <i class="fas fa-box me-1"></i>
                            <?php echo e($gift->stock_quantity ?? 0); ?> قطعة
                        </span>
                    </div>

                    <!-- Actions -->
                    <div class="gift-actions">
                        <a href="<?php echo e(route('vendor.gifts.show', $gift)); ?>" class="btn btn-success btn-sm">
                            <i class="fas fa-eye"></i>
                            عرض
                        </a>
                        <a href="<?php echo e(route('vendor.gifts.edit', $gift)); ?>" class="btn btn-warning btn-sm">
                            <i class="fas fa-edit"></i>
                            تعديل
                        </a>
                        <?php if(($gift->approval_status ?? 'pending') === 'pending'): ?>
                            <form action="<?php echo e(route('vendor.gifts.submit-for-approval', $gift)); ?>" method="POST" style="display: inline;">
                                <?php echo csrf_field(); ?>
                                <button type="submit" class="btn btn-success btn-sm">
                                    <i class="fas fa-paper-plane"></i>
                                    طلب موافقة
                                </button>
                            </form>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>

        <!-- Pagination -->
        <?php if($gifts->hasPages()): ?>
            <div class="d-flex justify-content-center mt-4">
                <?php echo e($gifts->links()); ?>

            </div>
        <?php endif; ?>
    <?php else: ?>
        <!-- Empty State -->
        <div class="empty-state">
            <div class="empty-icon">
                <i class="fas fa-gift"></i>
            </div>
            <h3 class="empty-title">لا توجد هدايا حتى الآن</h3>
            <p class="empty-subtitle">ابدأ بإضافة هداياك الأولى لتظهر هنا</p>
            <a href="<?php echo e(route('vendor.gifts.create')); ?>" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>
                أضف هدية جديدة
            </a>
        </div>
    <?php endif; ?>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add animation to cards
    const cards = document.querySelectorAll('.gift-card');
    cards.forEach((card, index) => {
        card.style.animationDelay = `${index * 0.1}s`;
        card.classList.add('fade-in');
    });
});
</script>
<?php $__env->stopPush(); ?> 
<?php echo $__env->make('layouts.vendor', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\GiftsSaudi-Laravel (1)\resources\views/vendor/gifts/index.blade.php ENDPATH**/ ?>