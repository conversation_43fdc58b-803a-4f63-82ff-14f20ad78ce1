

<?php $__env->startSection('title', 'تفاصيل الهدية'); ?>
<?php $__env->startSection('page-title', 'تفاصيل الهدية'); ?>

<?php $__env->startPush('styles'); ?>
<style>
    .detail-card {
        background: white;
        border-radius: 15px;
        padding: 30px;
        box-shadow: 0 5px 20px rgba(0,0,0,0.08);
        margin-bottom: 30px;
    }

    .gift-image-large {
        width: 100%;
        max-width: 400px;
        height: 300px;
        border-radius: 15px;
        object-fit: cover;
        background: #f8f9fa;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 4rem;
        color: #6c757d;
    }

    .status-badge {
        padding: 8px 16px;
        border-radius: 25px;
        font-size: 0.9rem;
        font-weight: 500;
    }

    .status-available { background: #d4edda; color: #155724; }
    .status-out_of_stock { background: #f8d7da; color: #721c24; }
    .status-discontinued { background: #d6d8db; color: #495057; }

    .approval-badge {
        padding: 8px 16px;
        border-radius: 25px;
        font-size: 0.9rem;
        font-weight: 500;
    }

    .approval-approved { background: #d4edda; color: #155724; }
    .approval-pending { background: #fff3cd; color: #856404; }

    .info-item {
        border-bottom: 1px solid #f8f9fa;
        padding: 15px 0;
    }

    .info-item:last-child {
        border-bottom: none;
    }

    .info-label {
        font-weight: 600;
        color: #495057;
        margin-bottom: 5px;
    }

    .info-value {
        color: #6c757d;
        font-size: 1.1rem;
    }

    .price-display {
        font-size: 2rem;
        font-weight: bold;
        color: #28a745;
    }
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
<div class="row">
    <!-- Gift Image and Basic Info -->
    <div class="col-lg-5">
        <div class="detail-card text-center">
            <div class="gift-image-large mx-auto mb-4">
                <?php if($gift->image): ?>
                    <img src="<?php echo e(asset('storage/' . $gift->image)); ?>" alt="<?php echo e($gift->name); ?>" class="gift-image-large">
                <?php else: ?>
                    <i class="fas fa-gift"></i>
                <?php endif; ?>
            </div>
            
            <h2 class="mb-3"><?php echo e($gift->name); ?></h2>
            
            <div class="price-display mb-3"><?php echo e(number_format($gift->price, 2)); ?> ريال</div>
            
            <div class="d-flex justify-content-center gap-3 mb-4">
                <span class="status-badge status-<?php echo e($gift->status); ?>">
                    <?php switch($gift->status):
                        case ('available'): ?> متاح <?php break; ?>
                        <?php case ('out_of_stock'): ?> نفد المخزون <?php break; ?>
                        <?php case ('discontinued'): ?> متوقف <?php break; ?>
                        <?php default: ?> <?php echo e($gift->status); ?>

                    <?php endswitch; ?>
                </span>
                
                <span class="approval-badge approval-<?php echo e($gift->approved ? 'approved' : 'pending'); ?>">
                    <?php echo e($gift->approved ? 'معتمد' : 'في الانتظار'); ?>

                </span>
            </div>
            
            <div class="d-flex justify-content-center gap-2">
                <a href="<?php echo e(route('admin.gifts.edit', $gift)); ?>" class="btn btn-primary">
                    <i class="fas fa-edit me-1"></i> تعديل
                </a>
                
                <?php if(!$gift->approved): ?>
                    <button type="button" class="btn btn-success" onclick="approveGift(<?php echo e($gift->id); ?>)">
                        <i class="fas fa-check me-1"></i> اعتماد
                    </button>
                <?php else: ?>
                    <button type="button" class="btn btn-warning" onclick="rejectGift(<?php echo e($gift->id); ?>)">
                        <i class="fas fa-times me-1"></i> رفض الاعتماد
                    </button>
                <?php endif; ?>
                
                <button type="button" class="btn btn-outline-danger" onclick="deleteGift(<?php echo e($gift->id); ?>)">
                    <i class="fas fa-trash me-1"></i> حذف
                </button>
                
                <a href="<?php echo e(route('admin.gifts.index')); ?>" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-right me-1"></i> العودة للقائمة
                </a>
            </div>
        </div>
    </div>

    <!-- Gift Details -->
    <div class="col-lg-7">
        <div class="detail-card">
            <h4 class="mb-4">تفاصيل الهدية</h4>
            
            <div class="info-item">
                <div class="info-label">اسم الهدية</div>
                <div class="info-value"><?php echo e($gift->name); ?></div>
            </div>

            <?php if($gift->description): ?>
                <div class="info-item">
                    <div class="info-label">الوصف</div>
                    <div class="info-value"><?php echo e($gift->description); ?></div>
                </div>
            <?php endif; ?>

            <div class="info-item">
                <div class="info-label">السعر</div>
                <div class="info-value"><?php echo e(number_format($gift->price, 2)); ?> ريال سعودي</div>
            </div>

            <div class="info-item">
                <div class="info-label">الفئة</div>
                <div class="info-value">
                    <span class="badge bg-secondary fs-6"><?php echo e($gift->category); ?></span>
                </div>
            </div>

            <div class="info-item">
                <div class="info-label">حالة المخزون</div>
                <div class="info-value">
                    <span class="status-badge status-<?php echo e($gift->status); ?>">
                        <?php switch($gift->status):
                            case ('available'): ?> متاح للطلب <?php break; ?>
                            <?php case ('out_of_stock'): ?> نفد المخزون <?php break; ?>
                            <?php case ('discontinued'): ?> متوقف الإنتاج <?php break; ?>
                            <?php default: ?> <?php echo e($gift->status); ?>

                        <?php endswitch; ?>
                    </span>
                </div>
            </div>

            <div class="info-item">
                <div class="info-label">حالة الاعتماد</div>
                <div class="info-value">
                    <span class="approval-badge approval-<?php echo e($gift->approved ? 'approved' : 'pending'); ?>">
                        <?php echo e($gift->approved ? 'معتمد من الإدارة' : 'في انتظار الاعتماد'); ?>

                    </span>
                </div>
            </div>

            <div class="info-item">
                <div class="info-label">تاريخ الإضافة</div>
                <div class="info-value"><?php echo e($gift->created_at->format('Y/m/d H:i')); ?></div>
            </div>

            <div class="info-item">
                <div class="info-label">آخر تحديث</div>
                <div class="info-value"><?php echo e($gift->updated_at->format('Y/m/d H:i')); ?></div>
            </div>
        </div>

        <!-- Vendor Information -->
        <div class="detail-card">
            <h4 class="mb-4">معلومات التاجر</h4>
            
            <div class="info-item">
                <div class="info-label">اسم التاجر</div>
                <div class="info-value"><?php echo e($gift->vendor->user->name); ?></div>
            </div>

            <?php if($gift->vendor->company_name): ?>
                <div class="info-item">
                    <div class="info-label">اسم الشركة</div>
                    <div class="info-value"><?php echo e($gift->vendor->company_name); ?></div>
                </div>
            <?php endif; ?>

            <?php if($gift->vendor->user->email): ?>
                <div class="info-item">
                    <div class="info-label">البريد الإلكتروني</div>
                    <div class="info-value">
                        <a href="mailto:<?php echo e($gift->vendor->user->email); ?>"><?php echo e($gift->vendor->user->email); ?></a>
                    </div>
                </div>
            <?php endif; ?>

            <?php if($gift->vendor->user->phone): ?>
                <div class="info-item">
                    <div class="info-label">رقم الهاتف</div>
                    <div class="info-value">
                        <a href="tel:<?php echo e($gift->vendor->user->phone); ?>"><?php echo e($gift->vendor->user->phone); ?></a>
                    </div>
                </div>
            <?php endif; ?>

            <?php if($gift->vendor->city): ?>
                <div class="info-item">
                    <div class="info-label">المدينة</div>
                    <div class="info-value"><?php echo e($gift->vendor->city); ?></div>
                </div>
            <?php endif; ?>

            <div class="info-item">
                <div class="info-label">حالة التاجر</div>
                <div class="info-value">
                    <?php if($gift->vendor->is_approved): ?>
                        <span class="badge bg-success">معتمد</span>
                    <?php else: ?>
                        <span class="badge bg-warning">في الانتظار</span>
                    <?php endif; ?>
                </div>
            </div>

            <div class="mt-3">
                <a href="<?php echo e(route('admin.vendors.show', $gift->vendor)); ?>" class="btn btn-outline-primary btn-sm">
                    <i class="fas fa-eye me-1"></i> عرض ملف التاجر
                </a>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
function approveGift(giftId) {
    if (confirm('هل أنت متأكد من اعتماد هذه الهدية؟')) {
        fetch(`/admin/gifts/${giftId}/approve`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('حدث خطأ في العملية');
            }
        });
    }
}

function rejectGift(giftId) {
    if (confirm('هل أنت متأكد من رفض اعتماد هذه الهدية؟')) {
        fetch(`/admin/gifts/${giftId}/reject`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('حدث خطأ في العملية');
            }
        });
    }
}

function deleteGift(giftId) {
    if (confirm('هل أنت متأكد من حذف هذه الهدية؟ هذا الإجراء لا يمكن التراجع عنه.')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/admin/gifts/${giftId}`;
        form.innerHTML = `
            <input type="hidden" name="_token" value="${document.querySelector('meta[name="csrf-token"]').getAttribute('content')}">
            <input type="hidden" name="_method" value="DELETE">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}
</script>
<?php $__env->stopPush(); ?> 
<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\GiftsSaudi-Laravel (1)\resources\views\admin\gifts\show.blade.php ENDPATH**/ ?>