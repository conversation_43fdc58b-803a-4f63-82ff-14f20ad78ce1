<div class="form-section">
    <h3 class="section-title">
        <i class="fas fa-credit-card stage-icon"></i>
        معالجة الدفع
    </h3>
    
    <?php if($campaign->platform == 'external'): ?>
        <!-- External Platform - No Payment Required -->
        <div class="text-center py-5">
            <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
            <h4 class="text-white">لا يتطلب دفع مسبق</h4>
            <p class="text-muted">
                تم اختيار المنصة الخارجية - سيتم إرسال الحملة للمراجعة والموافقة
            </p>
        </div>
    <?php else: ?>
        <!-- Own Platform - Payment Required -->
        <div class="payment-container">
            <!-- Payment Summary -->
            <div class="payment-summary-card mb-4 p-4 rounded" style="background: rgba(42, 42, 42, 0.6); border: 1px solid #555;">
                <h5 class="text-white mb-3">ملخص الطلب</h5>
                <div class="row">
                    <div class="col-md-6">
                        <div class="summary-item d-flex justify-content-between mb-2">
                            <span class="text-muted">اسم الحملة:</span>
                            <span class="text-white"><?php echo e($campaign->campaign_name); ?></span>
                        </div>
                        <div class="summary-item d-flex justify-content-between mb-2">
                            <span class="text-muted">عدد الرسائل:</span>
                            <span class="text-white"><?php echo e(number_format($campaign->message_count)); ?></span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="summary-item d-flex justify-content-between mb-2">
                            <span class="text-muted">المجموع الفرعي:</span>
                            <span class="text-white"><?php echo e(number_format($campaign->message_count * 0.25, 2)); ?> ريال</span>
                        </div>
                        <div class="summary-item d-flex justify-content-between border-top pt-2" style="border-color: #555 !important;">
                            <span class="text-orange h5">المجموع النهائي:</span>
                            <span class="text-orange h5"><?php echo e(number_format($campaign->message_count * 0.25 * 1.15, 2)); ?> ريال</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Payment Methods -->
            <div class="payment-methods-section">
                <h5 class="text-white mb-3">اختر طريقة الدفع</h5>
                
                <div class="row">
                    <div class="col-md-4">
                        <div class="payment-method-card p-3 rounded text-center" 
                             onclick="selectPaymentMethod('credit_card')"
                             data-method="credit_card">
                            <i class="fas fa-credit-card fa-3x text-orange mb-3"></i>
                            <h6 class="text-white">بطاقة ائتمانية</h6>
                            <small class="text-muted">Visa, MasterCard, Mada</small>
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <div class="payment-method-card p-3 rounded text-center" 
                             onclick="selectPaymentMethod('bank_transfer')"
                             data-method="bank_transfer">
                            <i class="fas fa-university fa-3x text-orange mb-3"></i>
                            <h6 class="text-white">حوالة بنكية</h6>
                            <small class="text-muted">تحويل مباشر</small>
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <div class="payment-method-card p-3 rounded text-center" 
                             onclick="selectPaymentMethod('digital_wallet')"
                             data-method="digital_wallet">
                            <i class="fas fa-mobile-alt fa-3x text-orange mb-3"></i>
                            <h6 class="text-white">محفظة رقمية</h6>
                            <small class="text-muted">Apple Pay, STC Pay</small>
                        </div>
                    </div>
                </div>

                <input type="hidden" name="payment_method" id="payment_method" value="">
            </div>
        </div>
    <?php endif; ?>
</div>

<style>
.payment-method-card {
    background: linear-gradient(135deg, #2a2a2a 0%, #3d3d3d 100%);
    border: 2px solid transparent;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    height: 120px;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.payment-method-card:hover {
    border-color: #ff6b35;
    box-shadow: 0 10px 30px rgba(255, 107, 53, 0.2);
}

.payment-method-card.selected {
    border-color: #f7931e;
    background: linear-gradient(135deg, rgba(255, 107, 53, 0.1), rgba(247, 147, 30, 0.1));
    box-shadow: 0 15px 40px rgba(255, 107, 53, 0.3);
}
</style>

<script>
function selectPaymentMethod(method) {
    // Update visual selection
    document.querySelectorAll('.payment-method-card').forEach(card => {
        card.classList.remove('selected');
    });
    document.querySelector(`[data-method="${method}"]`).classList.add('selected');
    
    // Update hidden input
    document.getElementById('payment_method').value = method;
}
</script> <?php /**PATH D:\GiftsSaudi-Laravel (1)\resources\views\admin\campaigns\stages\payment.blade.php ENDPATH**/ ?>