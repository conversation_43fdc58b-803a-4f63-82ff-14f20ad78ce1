

<?php $__env->startSection('title', 'تعديل الهدية'); ?>
<?php $__env->startSection('page-title', 'تعديل الهدية'); ?>

<?php $__env->startPush('styles'); ?>
<style>
    .form-card {
        background: white;
        border-radius: 15px;
        padding: 30px;
        box-shadow: 0 5px 20px rgba(0,0,0,0.08);
        margin-bottom: 30px;
    }

    .form-section {
        margin-bottom: 40px;
    }

    .section-title {
        font-size: 1.2rem;
        font-weight: 600;
        color: #495057;
        margin-bottom: 20px;
        padding-bottom: 10px;
        border-bottom: 2px solid #f8f9fa;
    }

    .form-label {
        font-weight: 500;
        color: #495057;
        margin-bottom: 8px;
    }

    .form-control, .form-select {
        border-radius: 8px;
        border: 1px solid #dee2e6;
        padding: 12px 15px;
        transition: all 0.3s ease;
    }

    .form-control:focus, .form-select:focus {
        border-color: #2C5530;
        box-shadow: 0 0 0 0.2rem rgba(44, 85, 48, 0.25);
    }

    .btn {
        border-radius: 8px;
        padding: 12px 25px;
        font-weight: 500;
        transition: all 0.3s ease;
    }

    .alert {
        border-radius: 10px;
        border: none;
    }

    .image-preview {
        max-width: 200px;
        max-height: 200px;
        border-radius: 10px;
        margin-top: 15px;
    }

    .current-image {
        position: relative;
        display: inline-block;
    }

    .remove-image {
        position: absolute;
        top: -10px;
        right: -10px;
        background: #dc3545;
        color: white;
        border: none;
        border-radius: 50%;
        width: 30px;
        height: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 14px;
    }
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
<?php if($errors->any()): ?>
    <div class="alert alert-danger">
        <ul class="mb-0">
            <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <li><?php echo e($error); ?></li>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </ul>
    </div>
<?php endif; ?>

<form action="<?php echo e(route('admin.gifts.update', $gift)); ?>" method="POST" enctype="multipart/form-data">
    <?php echo csrf_field(); ?>
    <?php echo method_field('PUT'); ?>

    <div class="row">
        <div class="col-lg-8">
            <div class="form-card">
                <div class="form-section">
                    <h4 class="section-title">معلومات الهدية الأساسية</h4>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="name" class="form-label">اسم الهدية <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="name" name="name" 
                                   value="<?php echo e(old('name', $gift->name)); ?>" required>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="price" class="form-label">السعر (ريال) <span class="text-danger">*</span></label>
                            <input type="number" class="form-control" id="price" name="price" 
                                   step="0.01" min="0" value="<?php echo e(old('price', $gift->price)); ?>" required>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="category" class="form-label">الفئة <span class="text-danger">*</span></label>
                            <select class="form-select" id="category" name="category" required>
                                <option value="">اختر الفئة</option>
                                <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($category); ?>" 
                                            <?php echo e(old('category', $gift->category) == $category ? 'selected' : ''); ?>>
                                        <?php echo e($category); ?>

                                    </option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="status" class="form-label">حالة المخزون <span class="text-danger">*</span></label>
                            <select class="form-select" id="status" name="status" required>
                                <option value="available" <?php echo e(old('status', $gift->status) == 'available' ? 'selected' : ''); ?>>
                                    متاح
                                </option>
                                <option value="out_of_stock" <?php echo e(old('status', $gift->status) == 'out_of_stock' ? 'selected' : ''); ?>>
                                    نفد المخزون
                                </option>
                                <option value="discontinued" <?php echo e(old('status', $gift->status) == 'discontinued' ? 'selected' : ''); ?>>
                                    متوقف
                                </option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="description" class="form-label">وصف الهدية</label>
                        <textarea class="form-control" id="description" name="description" rows="4" 
                                  placeholder="أدخل وصفاً مفصلاً للهدية..."><?php echo e(old('description', $gift->description)); ?></textarea>
                    </div>
                </div>

                <div class="form-section">
                    <h4 class="section-title">صورة الهدية</h4>
                    
                    <?php if($gift->image): ?>
                        <div class="current-image mb-3">
                            <img src="<?php echo e(asset('storage/' . $gift->image)); ?>" alt="<?php echo e($gift->name); ?>" class="image-preview">
                            <button type="button" class="remove-image" onclick="removeCurrentImage()">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                        <input type="hidden" name="remove_image" id="remove_image" value="0">
                    <?php endif; ?>
                    
                    <div class="mb-3">
                        <label for="image" class="form-label">
                            <?php echo e($gift->image ? 'تغيير الصورة' : 'رفع صورة'); ?>

                        </label>
                        <input type="file" class="form-control" id="image" name="image" accept="image/*" 
                               onchange="previewImage(this)">
                        <small class="text-muted">يُفضل أن تكون الصورة بأبعاد مربعة (500x500 بكسل) وحجم أقل من 2MB</small>
                    </div>
                    
                    <div id="imagePreview" style="display: none;">
                        <img id="preview" class="image-preview" alt="معاينة الصورة">
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="form-card">
                <h4 class="section-title">إعدادات متقدمة</h4>
                
                <div class="mb-3">
                    <label for="vendor_id" class="form-label">التاجر</label>
                    <select class="form-select" id="vendor_id" name="vendor_id" disabled>
                        <option value="<?php echo e($gift->vendor_id); ?>">
                            <?php echo e($gift->vendor->company_name ?? $gift->vendor->user->name); ?>

                        </option>
                    </select>
                    <small class="text-muted">لا يمكن تغيير التاجر بعد إنشاء الهدية</small>
                </div>
                
                <div class="mb-3">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" value="1" id="approved" name="approved"
                               <?php echo e(old('approved', $gift->approved) ? 'checked' : ''); ?>>
                        <label class="form-check-label" for="approved">
                            <strong>معتمد للعرض</strong>
                        </label>
                        <div class="form-text">
                            عند التفعيل، ستظهر الهدية للعملاء في الموقع
                        </div>
                    </div>
                </div>

                <div class="mb-4">
                    <h6 class="mb-2">معلومات إضافية:</h6>
                    <div class="small text-muted">
                        <div class="mb-1"><strong>تاريخ الإنشاء:</strong> <?php echo e($gift->created_at->format('Y/m/d H:i')); ?></div>
                        <div class="mb-1"><strong>آخر تحديث:</strong> <?php echo e($gift->updated_at->format('Y/m/d H:i')); ?></div>
                        <div class="mb-1"><strong>رقم الهدية:</strong> #<?php echo e($gift->id); ?></div>
                    </div>
                </div>

                <div class="d-grid gap-2">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-1"></i> حفظ التغييرات
                    </button>
                    
                    <a href="<?php echo e(route('admin.gifts.show', $gift)); ?>" class="btn btn-outline-secondary">
                        <i class="fas fa-eye me-1"></i> عرض الهدية
                    </a>
                    
                    <a href="<?php echo e(route('admin.gifts.index')); ?>" class="btn btn-outline-dark">
                        <i class="fas fa-arrow-right me-1"></i> العودة للقائمة
                    </a>
                </div>
            </div>
        </div>
    </div>
</form>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
function previewImage(input) {
    const previewContainer = document.getElementById('imagePreview');
    const preview = document.getElementById('preview');
    
    if (input.files && input.files[0]) {
        const reader = new FileReader();
        
        reader.onload = function(e) {
            preview.src = e.target.result;
            previewContainer.style.display = 'block';
        };
        
        reader.readAsDataURL(input.files[0]);
    } else {
        previewContainer.style.display = 'none';
    }
}

function removeCurrentImage() {
    if (confirm('هل أنت متأكد من حذف الصورة الحالية؟')) {
        document.getElementById('remove_image').value = '1';
        document.querySelector('.current-image').style.display = 'none';
    }
}

// Form validation
document.querySelector('form').addEventListener('submit', function(e) {
    const name = document.getElementById('name').value.trim();
    const price = document.getElementById('price').value;
    const category = document.getElementById('category').value;
    const status = document.getElementById('status').value;
    
    if (!name) {
        alert('يرجى إدخال اسم الهدية');
        e.preventDefault();
        return;
    }
    
    if (!price || price <= 0) {
        alert('يرجى إدخال سعر صحيح للهدية');
        e.preventDefault();
        return;
    }
    
    if (!category) {
        alert('يرجى اختيار فئة الهدية');
        e.preventDefault();
        return;
    }
    
    if (!status) {
        alert('يرجى اختيار حالة المخزون');
        e.preventDefault();
        return;
    }
});

// Auto-save draft (optional enhancement)
let autoSaveTimer;
const formElements = ['name', 'price', 'category', 'status', 'description'];

formElements.forEach(elementId => {
    document.getElementById(elementId).addEventListener('input', function() {
        clearTimeout(autoSaveTimer);
        autoSaveTimer = setTimeout(function() {
            // Here you could implement auto-save to localStorage or server
            console.log('Auto-save draft...');
        }, 2000);
    });
});
</script>
<?php $__env->stopPush(); ?> 
<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\GiftsSaudi-Laravel (1)\resources\views\admin\gifts\edit.blade.php ENDPATH**/ ?>