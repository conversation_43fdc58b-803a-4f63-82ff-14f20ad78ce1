<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    <title><?php echo $__env->yieldContent('title', 'لوحة التحكم'); ?> - هدايا السعودية</title>

    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #1a1a1a;
            --secondary-color: #2d2d2d;
            --accent-color: #ff6b35;
            --success-color: #00d4aa;
            --warning-color: #ffc107;
            --danger-color: #ff4757;
            --info-color: #3867d6;
            --light-color: #f8f9fa;
            --dark-color: #0f0f0f;
            --sidebar-width: 280px;
            --navbar-height: 70px;
            --bs-secondary-color: rgba(222, 226, 230, 0.75);
            --bs-secondary-color-rgb: 222, 226, 230;
            --bs-tertiary-color: rgba(222, 226, 230, 0.5);
            --bs-tertiary-color-rgb: 222, 226, 230;
            --bs-body-color: #e0e0e0;
            --bs-body-color-rgb: 224, 224, 224;
            --bs-emphasis-color: #ffffff;
            --bs-emphasis-color-rgb: 255, 255, 255;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, var(--dark-color) 0%, var(--primary-color) 100%);
            color: #e0e0e0;
            overflow-x: hidden;
        }

        /* Professional Sidebar */
        .sidebar {
            position: fixed;
            top: 0;
            right: 0;
            width: var(--sidebar-width);
            height: 100vh;
            background: linear-gradient(180deg, #1a1a1a 0%, #0f0f0f 100%);
            backdrop-filter: blur(20px);
            border-left: 1px solid rgba(255, 107, 53, 0.1);
            z-index: 1000;
            transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            box-shadow: -10px 0 30px rgba(0, 0, 0, 0.5);
        }

        .sidebar.collapsed {
            width: 80px;
        }

        /* Sidebar Header */
        .sidebar-header {
            height: var(--navbar-height);
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, var(--accent-color) 0%, #f7931e 100%);
            position: relative;
            overflow: hidden;
        }

        .sidebar-header::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
            transform: rotate(45deg);
            animation: shimmer 3s ease-in-out infinite;
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
        }

        .sidebar-logo {
            display: flex;
            align-items: center;
            color: white;
            text-decoration: none;
            font-weight: 800;
            font-size: 1.2rem;
            z-index: 2;
            position: relative;
        }

        .sidebar-logo i {
            font-size: 2rem;
            margin-left: 15px;
        }

        .sidebar.collapsed .logo-text {
            display: none;
        }

        /* Navigation Menu */
        .sidebar-nav {
            padding: 20px 0;
            height: calc(100vh - var(--navbar-height));
            overflow-y: auto;
            overflow-x: hidden;
        }

        .sidebar-nav::-webkit-scrollbar {
            width: 4px;
        }

        .sidebar-nav::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
        }

        .sidebar-nav::-webkit-scrollbar-thumb {
            background: var(--accent-color);
            border-radius: 2px;
        }

        .nav-section {
            margin-bottom: 30px;
        }

        .nav-section-title {
            color: #888;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
            padding: 0 25px;
            margin-bottom: 15px;
        }

        .sidebar.collapsed .nav-section-title {
            display: none;
        }

        .nav-item {
            margin-bottom: 5px;
        }

        .nav-link {
            display: flex;
            align-items: center;
            padding: 15px 25px;
            color: #b0b0b0;
            text-decoration: none;
            transition: all 0.3s ease;
            position: relative;
            border-radius: 0 25px 25px 0;
            margin-left: 15px;
        }

        .nav-link::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 4px;
            height: 100%;
            background: var(--accent-color);
            transform: scaleY(0);
            transition: transform 0.3s ease;
        }

        .nav-link:hover,
        .nav-link.active {
            color: white;
            background: linear-gradient(135deg, rgba(255, 107, 53, 0.2) 0%, rgba(255, 107, 53, 0.05) 100%);
            transform: translateX(-5px);
        }

        .nav-link:hover::before,
        .nav-link.active::before {
            transform: scaleY(1);
        }

        .nav-link i {
            font-size: 1.25rem;
            width: 25px;
            margin-left: 15px;
            transition: all 0.3s ease;
        }

        .nav-link:hover i {
            transform: scale(1.1);
            color: var(--accent-color);
        }

        .sidebar.collapsed .nav-link {
            justify-content: center;
            padding: 15px;
        }

        .sidebar.collapsed .nav-link span {
            display: none;
        }

        .sidebar.collapsed .nav-link i {
            margin-left: 0;
        }

        /* Badge for notifications */
        .nav-badge {
            background: var(--accent-color);
            color: white;
            font-size: 0.7rem;
            padding: 2px 6px;
            border-radius: 10px;
            margin-right: auto;
        }

        .sidebar.collapsed .nav-badge {
            display: none;
        }

        /* Professional Navbar */
        .main-navbar {
            position: fixed;
            top: 0;
            left: 0;
            right: var(--sidebar-width);
            height: var(--navbar-height);
            background: linear-gradient(135deg, #2d2d2d 0%, #1a1a1a 100%);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(255, 107, 53, 0.1);
            z-index: 999;
            display: flex;
            align-items: center;
            padding: 0 30px;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.3);
            transition: all 0.3s ease;
        }

        .sidebar.collapsed ~ .main-content .main-navbar {
            right: 80px;
        }

        .navbar-left {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .sidebar-toggle {
            background: none;
            border: none;
            color: #b0b0b0;
            font-size: 1.5rem;
            cursor: pointer;
            padding: 10px;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .sidebar-toggle:hover {
            color: var(--accent-color);
            background: rgba(255, 107, 53, 0.1);
        }

        .breadcrumb-nav {
            display: flex;
            align-items: center;
            gap: 10px;
            color: #888;
        }

        .breadcrumb-nav a {
            color: var(--accent-color);
            text-decoration: none;
        }

        .navbar-right {
            margin-right: auto;
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .search-box {
            position: relative;
        }

        .search-input {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 107, 53, 0.2);
            border-radius: 25px;
            padding: 10px 40px 10px 20px;
            color: white;
            width: 300px;
            transition: all 0.3s ease;
        }

        .search-input:focus {
            outline: none;
            border-color: var(--accent-color);
            box-shadow: 0 0 20px rgba(255, 107, 53, 0.3);
        }

        .search-input::placeholder {
            color: #888;
        }

        .search-btn {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: var(--accent-color);
            cursor: pointer;
        }

        .navbar-actions {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .action-btn {
            position: relative;
            background: none;
            border: none;
            color: #b0b0b0;
            font-size: 1.2rem;
            padding: 10px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .action-btn:hover {
            color: white;
            background: rgba(255, 107, 53, 0.1);
        }

        .action-btn .badge {
            position: absolute;
            top: 5px;
            right: 5px;
            background: var(--danger-color);
            color: white;
            font-size: 0.7rem;
            padding: 2px 5px;
            border-radius: 10px;
            min-width: 18px;
            text-align: center;
        }

        .user-dropdown {
            position: relative;
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--accent-color) 0%, #f7931e 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .user-avatar:hover {
            transform: scale(1.1);
            box-shadow: 0 5px 20px rgba(255, 107, 53, 0.4);
        }

        /* Main Content */
        .main-content {
            margin-right: var(--sidebar-width);
            margin-top: var(--navbar-height);
            min-height: calc(100vh - var(--navbar-height));
            padding: 30px;
            transition: margin-right 0.3s ease;
        }

        .sidebar.collapsed ~ .main-content {
            margin-right: 80px;
        }

        /* Page Header */
        .page-header {
            background: linear-gradient(135deg, #2d2d2d 0%, #1a1a1a 100%);
            border: 1px solid rgba(255, 107, 53, 0.1);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }

        .page-title {
            color: white;
            font-weight: 700;
            font-size: 2rem;
            margin-bottom: 10px;
        }

        .page-subtitle {
            color: #888;
            font-size: 1rem;
        }

        /* Content Cards */
        .content-card {
            background: linear-gradient(145deg, #2d2d2d, #1a1a1a);
            border: 1px solid rgba(255, 107, 53, 0.1);
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            overflow: hidden;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .sidebar {
                width: 100%;
                transform: translateX(100%);
            }

            .sidebar.show {
                transform: translateX(0);
            }

            .main-navbar {
                right: 0;
            }

            .main-content {
                margin-right: 0;
            }

            .search-input {
                width: 200px;
            }
        }

        /* Loading Animation */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.9);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .loading-overlay.show {
            opacity: 1;
            visibility: visible;
        }

        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 3px solid rgba(255, 107, 53, 0.3);
            border-top: 3px solid var(--accent-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Utility Classes */
        .text-orange {
            color: var(--accent-color) !important;
        }

        /* Force visible text colors for all elements */
        .text-secondary {
            color: #ccc !important;
        }
        
        .text-body-secondary {
            color: #ccc !important;
        }
        
        * {
            /* Fallback for any element using var(--bs-secondary-color) */
        }
        
        /* Override any Bootstrap secondary colors that might be invisible */
        small, .small {
            color: #888 !important;
        }
        
        .form-text {
            color: #888 !important;
        }

        /* Fix white background elements with proper text colors */
        .bg-white,
        .table-responsive .table,
        .dropdown-menu,
        .form-select,
        .form-control,
        .modal-content {
            background: white !important;
            color: #212529 !important;
        }

        /* Ensure dropdown items are visible */
        .dropdown-menu .dropdown-item {
            color: #212529 !important;
        }

        .dropdown-menu .dropdown-item:hover,
        .dropdown-menu .dropdown-item:focus {
            background-color: #f8f9fa !important;
            color: #212529 !important;
        }

        /* Fix table styling */
        .table {
            color: #212529 !important;
        }

        .table thead th {
            background-color: #f8f9fa !important;
            color: white !important;
            border-bottom: 2px solid #dee2e6 !important;
            font-weight: 600 !important;
            padding: 15px 12px !important;
            text-align: center !important;
            vertical-align: middle !important;
        }

        .table tbody td {
            color: #212529 !important;
            border-top: 1px solid #dee2e6 !important;
            padding: 15px 12px !important;
            text-align: center !important;
            vertical-align: middle !important;
        }

        /* Global vendor-name styling for all tables */
        .vendor-name {
            color: #000000 !important;
            font-weight: 600 !important;
        }

        .table-dark {
            background-color: #212529 !important;
            color: #fff !important;
        }

        .table-dark thead th,
        .table-dark tbody td {
            background-color: #212529 !important;
            color: #fff !important;
        }

        /* Fix form elements */
        .form-control,
        .form-select {
            background-color: #fff !important;
            color: #212529 !important;
            border: 1px solid #ced4da !important;
        }

        .form-control:focus,
        .form-select:focus {
            background-color: #fff !important;
            color: #212529 !important;
            border-color: #86b7fe !important;
        }

        .form-control::placeholder {
            color: #6c757d !important;
        }

        /* Fix text colors on white backgrounds */
        .bg-white * {
            color: #212529 !important;
        }

        /* Override any conflicting dark theme styles for white backgrounds */
        .bg-white .text-muted,
        .bg-white .text-secondary {
            color: #6c757d !important;
        }

        /* FORCE OVERRIDE Bootstrap Pagination - Higher Specificity */
        nav[aria-label="pagination"] .pagination,
        .pagination,
        ul.pagination {
            justify-content: center !important;
            margin: 20px 0 !important;
            gap: 5px !important;
            display: flex !important;
            align-items: center !important;
            padding-left: 0 !important;
            list-style: none !important;
            border-radius: 0 !important;
        }

        nav[aria-label="pagination"] .pagination .page-item,
        .pagination .page-item,
        ul.pagination .page-item {
            margin: 0 3px !important;
            display: inline-block !important;
        }

        nav[aria-label="pagination"] .pagination .page-link,
        .pagination .page-link,
        ul.pagination .page-link {
            background: #ffffff !important;
            border: 1px solid #dee2e6 !important;
            color: #495057 !important;
            border-radius: 6px !important;
            padding: 6px 10px !important;
            font-weight: 500 !important;
            min-width: 32px !important;
            height: 32px !important;
            text-align: center !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            font-size: 13px !important;
            transition: all 0.3s ease !important;
            text-decoration: none !important;
            line-height: 1 !important;
            position: relative !important;
        }

        /* Remove Bootstrap's pseudo elements completely */
        nav[aria-label="pagination"] .pagination .page-link::before,
        nav[aria-label="pagination"] .pagination .page-link::after,
        .pagination .page-link::before,
        .pagination .page-link::after,
        ul.pagination .page-link::before,
        ul.pagination .page-link::after {
            content: none !important;
            display: none !important;
        }

        nav[aria-label="pagination"] .pagination .page-link:hover,
        .pagination .page-link:hover,
        ul.pagination .page-link:hover {
            background: #ff6b35 !important;
            border-color: #ff6b35 !important;
            color: #ffffff !important;
            transform: translateY(-2px) !important;
            box-shadow: 0 4px 12px rgba(255, 107, 53, 0.3) !important;
            z-index: 3 !important;
        }

        nav[aria-label="pagination"] .pagination .page-item.active .page-link,
        .pagination .page-item.active .page-link,
        ul.pagination .page-item.active .page-link {
            background: #ff6b35 !important;
            border-color: #ff6b35 !important;
            color: #ffffff !important;
            box-shadow: 0 3px 10px rgba(255, 107, 53, 0.4) !important;
            z-index: 3 !important;
        }

        nav[aria-label="pagination"] .pagination .page-item.disabled .page-link,
        .pagination .page-item.disabled .page-link,
        ul.pagination .page-item.disabled .page-link {
            background: #f8f9fa !important;
            border-color: #dee2e6 !important;
            color: #6c757d !important;
            cursor: not-allowed !important;
            opacity: 0.5 !important;
            pointer-events: none !important;
        }

        /* Specific targeting for Laravel's pagination arrows */
        nav[aria-label="pagination"] .pagination .page-link[rel="prev"],
        nav[aria-label="pagination"] .pagination .page-link[rel="next"],
        .pagination .page-link[rel="prev"],
        .pagination .page-link[rel="next"],
        .pagination .page-link[aria-label="Previous"],
        .pagination .page-link[aria-label="Next"],
        .pagination .page-item:first-child .page-link,
        .pagination .page-item:last-child .page-link {
            padding: 6px 10px !important;
            font-size: 13px !important;
            background: #ffffff !important;
            border: 1px solid #dee2e6 !important;
            color: #495057 !important;
            min-width: 32px !important;
            height: 32px !important;
            overflow: hidden !important;
        }

        /* Replace Laravel's default pagination text with clean arrows */
        nav[aria-label="pagination"] .pagination .page-link[rel="prev"]:before,
        .pagination .page-link[rel="prev"]:before,
        .pagination .page-link[aria-label="Previous"]:before,
        .pagination .page-item:first-child .page-link:before {
            content: "‹" !important;
            display: inline-block !important;
            font-size: 13px !important;
            line-height: 1 !important;
            font-weight: 500 !important;
        }

        nav[aria-label="pagination"] .pagination .page-link[rel="next"]:before,
        .pagination .page-link[rel="next"]:before,
        .pagination .page-link[aria-label="Next"]:before,
        .pagination .page-item:last-child .page-link:before {
            content: "›" !important;
            display: inline-block !important;
            font-size: 13px !important;
            line-height: 1 !important;
            font-weight: 500 !important;
        }

        /* Hide Laravel's default text in prev/next buttons */
        nav[aria-label="pagination"] .pagination .page-link[rel="prev"] *,
        nav[aria-label="pagination"] .pagination .page-link[rel="next"] *,
        .pagination .page-link[rel="prev"] *,
        .pagination .page-link[rel="next"] *,
        .pagination .page-link[aria-label="Previous"] *,
        .pagination .page-link[aria-label="Next"] *,
        .pagination .page-item:first-child .page-link *,
        .pagination .page-item:last-child .page-link * {
            display: none !important;
        }

        .table thead th {
            background-color: #f8f9fa !important;
            color: white !important;
            border-bottom: 2px solid #dee2e6 !important;
            font-weight: 600 !important;
            padding: 15px 12px !important;
            text-align: center !important;
            vertical-align: middle !important;
        }

        .table tbody td {
            color: #212529 !important;
            border-top: 1px solid #dee2e6 !important;
            padding: 15px 12px !important;
            text-align: center !important;
            vertical-align: middle !important;
        }

        /* Global vendor-name styling for all tables */
        .vendor-name {
            color: #000000 !important;
            font-weight: 600 !important;
        }

        /* Table column width adjustments */
        .table th:nth-child(1), .table td:nth-child(1) { min-width: 200px !important; }
        .table th:nth-child(2), .table td:nth-child(2) { min-width: 180px !important; }
        .table th:nth-child(3), .table td:nth-child(3) { min-width: 160px !important; }
        .table th:nth-child(4), .table td:nth-child(4) { min-width: 120px !important; }
        .table th:nth-child(5), .table td:nth-child(5) { min-width: 120px !important; }
        .table th:nth-child(6), .table td:nth-child(6) { min-width: 100px !important; }
        .table th:last-child, .table td:last-child { min-width: 150px !important; }

        .table-dark {
            background-color: #212529 !important;
            color: #fff !important;
        }

        .table-dark thead th,
        .table-dark tbody td {
            background-color: #212529 !important;
            color: #fff !important;
        }

        /* Custom Pagination Override - Load AFTER Bootstrap */
        * .pagination,
        *[class*="pagination"],
        nav .pagination,
        .d-flex .pagination {
            background: none !important;
            border: none !important;
            margin: 20px auto !important;
            display: flex !important;
            justify-content: center !important;
            align-items: center !important;
            gap: 8px !important;
            padding: 0 !important;
            list-style: none !important;
        }

        * .pagination .page-item,
        * .pagination li {
            background: none !important;
            border: none !important;
            margin: 0 2px !important;
            padding: 0 !important;
            display: inline-flex !important;
        }

        * .pagination .page-link,
        * .pagination a,
        * .pagination span {
            background: #ffffff !important;
            color: #495057 !important;
            border: 1px solid #dee2e6 !important;
            border-radius: 6px !important;
            padding: 6px 10px !important;
            min-width: 32px !important;
            height: 32px !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            text-decoration: none !important;
            font-size: 13px !important;
            font-weight: 500 !important;
            transition: all 0.2s ease !important;
            position: relative !important;
            overflow: hidden !important;
            line-height: 1 !important;
        }

        * .pagination .page-link:hover,
        * .pagination a:hover {
            background: #ff6b35 !important;
            color: #ffffff !important;
            border-color: #ff6b35 !important;
            transform: translateY(-1px) !important;
            box-shadow: 0 2px 8px rgba(255, 107, 53, 0.3) !important;
        }

        * .pagination .page-item.active .page-link,
        * .pagination .active a,
        * .pagination .active span {
            background: #ff6b35 !important;
            color: #ffffff !important;
            border-color: #ff6b35 !important;
            font-weight: 600 !important;
        }

        * .pagination .page-item.disabled .page-link,
        * .pagination .disabled a,
        * .pagination .disabled span {
            background: #f8f9fa !important;
            color: #6c757d !important;
            border-color: #dee2e6 !important;
            cursor: not-allowed !important;
            opacity: 0.6 !important;
        }

        /* Hide all default arrows and symbols */
        * .pagination .page-link::before,
        * .pagination .page-link::after,
        * .pagination a::before,
        * .pagination a::after,
        * .pagination span::before,
        * .pagination span::after {
            display: none !important;
            content: none !important;
        }

        /* Custom clean arrows for prev/next */
        * .pagination .page-link[rel="prev"]::before,
        * .pagination a[rel="prev"]::before,
        * .pagination .page-item:first-child .page-link::before,
        * .pagination .page-item:first-child a::before {
            content: "‹" !important;
            display: inline-block !important;
            font-size: 13px !important;
            font-weight: 500 !important;
            line-height: 1 !important;
        }

        * .pagination .page-link[rel="next"]::before,
        * .pagination a[rel="next"]::before,
        * .pagination .page-item:last-child .page-link::before,
        * .pagination .page-item:last-child a::before {
            content: "›" !important;
            display: inline-block !important;
            font-size: 13px !important;
            font-weight: 500 !important;
            line-height: 1 !important;
        }

        /* Hide Laravel's default pagination text */
        * .pagination .page-link[rel="prev"] *:not(::before),
        * .pagination .page-link[rel="next"] *:not(::before),
        * .pagination a[rel="prev"] *:not(::before),
        * .pagination a[rel="next"] *:not(::before),
        * .pagination .page-item:first-child .page-link *:not(::before),
        * .pagination .page-item:last-child .page-link *:not(::before),
        * .pagination .page-item:first-child a *:not(::before),
        * .pagination .page-item:last-child a *:not(::before) {
            display: none !important;
        }

        /* Force consistent styling for all pagination content including icons and special characters */
        * .pagination .page-link *,
        * .pagination a *,
        * .pagination span *,
        * .pagination .page-link i,
        * .pagination a i,
        * .pagination span i,
        * .pagination .page-link svg,
        * .pagination a svg,
        * .pagination span svg {
            font-size: 13px !important;
            font-weight: 500 !important;
            line-height: 1 !important;
            width: auto !important;
            height: auto !important;
            vertical-align: baseline !important;
        }

        /* Ensure special characters and symbols match number styling */
        * .pagination .page-link:not([rel]):not([aria-label]),
        * .pagination a:not([rel]):not([aria-label]),
        * .pagination span:not([rel]):not([aria-label]) {
            font-size: 13px !important;
            font-weight: 500 !important;
            line-height: 1 !important;
        }

        /* Fix screen reader text */
        .sr-only {
            position: absolute !important;
            width: 1px !important;
            height: 1px !important;
            padding: 0 !important;
            margin: -1px !important;
            overflow: hidden !important;
            clip: rect(0, 0, 0, 0) !important;
            white-space: nowrap !important;
            border: 0 !important;
        }
    </style>

    <?php echo $__env->yieldPushContent('styles'); ?>
</head>
<body>
    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-spinner"></div>
    </div>

    <!-- Sidebar -->
    <aside class="sidebar" id="sidebar">
        <!-- Sidebar Header -->
        <div class="sidebar-header">
            <a href="<?php echo e(route('admin.dashboard')); ?>" class="sidebar-logo">
                <i class="fas fa-gifts"></i>
                <span class="logo-text">هدايا السعودية</span>
            </a>
        </div>

        <!-- Navigation Menu -->
        <nav class="sidebar-nav">
            <!-- Dashboard Section -->
            <div class="nav-section">
                <div class="nav-section-title">الرئيسية</div>
                <div class="nav-item">
                    <a href="<?php echo e(route('admin.dashboard')); ?>" class="nav-link <?php echo e(request()->routeIs('admin.dashboard') ? 'active' : ''); ?>">
                        <i class="fas fa-tachometer-alt"></i>
                        <span>لوحة التحكم</span>
                    </a>
                </div>
            </div>

            <!-- Management Section -->
            <div class="nav-section">
                <div class="nav-section-title">الإدارة</div>
                
                <div class="nav-item">
                    <a href="<?php echo e(route('admin.vendors.index')); ?>" class="nav-link <?php echo e(request()->routeIs('admin.vendors.*') ? 'active' : ''); ?>">
                        <i class="fas fa-store"></i>
                        <span>إدارة التجار</span>
                        <?php if(isset($pendingVendors) && $pendingVendors > 0): ?>
                            <span class="nav-badge"><?php echo e($pendingVendors); ?></span>
                        <?php endif; ?>
                    </a>
                </div>

                <div class="nav-item">
                    <a href="<?php echo e(route('admin.clients.index')); ?>" class="nav-link <?php echo e(request()->routeIs('admin.clients.*') ? 'active' : ''); ?>">
                        <i class="fas fa-users"></i>
                        <span>إدارة العملاء</span>
                    </a>
                </div>

                <div class="nav-item">
                    <a href="<?php echo e(route('admin.gifts.index')); ?>" class="nav-link <?php echo e(request()->routeIs('admin.gifts.*') ? 'active' : ''); ?>">
                        <i class="fas fa-gift"></i>
                        <span>إدارة الهدايا</span>
                    </a>
                </div>

                <div class="nav-item">
                    <a href="<?php echo e(route('admin.campaigns.index')); ?>" class="nav-link <?php echo e(request()->routeIs('admin.campaigns.*') ? 'active' : ''); ?>">
                        <i class="fas fa-bullhorn"></i>
                        <span>إدارة الحملات</span>
                        <?php
                            $pendingCampaigns = App\Models\GiftCampaign::pendingApproval()->count();
                        ?>
                        <?php if($pendingCampaigns > 0): ?>
                            <span class="nav-badge"><?php echo e($pendingCampaigns); ?></span>
                        <?php endif; ?>
                    </a>
                </div>
            </div>

            <!-- Financial Section -->
            <div class="nav-section">
                <div class="nav-section-title">المالية</div>
                
                <div class="nav-item">
                    <a href="<?php echo e(route('admin.payments.index')); ?>" class="nav-link <?php echo e(request()->routeIs('admin.payments.*') ? 'active' : ''); ?>">
                        <i class="fas fa-credit-card"></i>
                        <span>المدفوعات</span>
                    </a>
                </div>

                <div class="nav-item">
                    <a href="<?php echo e(route('admin.campaigns.financial-reports')); ?>" class="nav-link <?php echo e(request()->routeIs('admin.campaigns.financial-reports') || request()->routeIs('admin.campaigns.vendor-financial-details') ? 'active' : ''); ?>">
                        <i class="fas fa-chart-line"></i>
                        <span>التقارير المالية</span>
                    </a>
                </div>
            </div>

            <!-- Logout Section -->
            <div class="nav-section">
                <div class="nav-item">
                    <form method="POST" action="<?php echo e(route('logout')); ?>" class="d-inline">
                        <?php echo csrf_field(); ?>
                        <button type="submit" class="nav-link border-0 bg-transparent w-100 text-start">
                            <i class="fas fa-sign-out-alt"></i>
                            <span>تسجيل الخروج</span>
                        </button>
                    </form>
                </div>
            </div>
        </nav>
    </aside>

    <!-- Main Content Area -->
    <div class="main-content">
        <!-- Top Navbar -->
        <nav class="main-navbar">
            <div class="navbar-left">
                <button class="sidebar-toggle" id="sidebarToggle">
                    <i class="fas fa-bars"></i>
                </button>
                
                <div class="breadcrumb-nav">
                    <a href="<?php echo e(route('admin.dashboard')); ?>">الرئيسية</a>
                    <?php if(!request()->routeIs('admin.dashboard')): ?>
                        <i class="fas fa-chevron-left"></i>
                        <span><?php echo $__env->yieldContent('page-title', 'الصفحة'); ?></span>
                    <?php endif; ?>
                </div>
            </div>

            <div class="navbar-right">
                <!-- Search Box -->
                <div class="search-box">
                    <input type="text" class="search-input" placeholder="البحث...">
                    <button class="search-btn">
                        <i class="fas fa-search"></i>
                    </button>
                </div>

                <!-- Action Buttons -->
                <div class="navbar-actions">
                    <button class="action-btn" title="الإشعارات">
                        <i class="fas fa-bell"></i>
                        <span class="badge">3</span>
                    </button>
                    
                    <button class="action-btn" title="الرسائل">
                        <i class="fas fa-envelope"></i>
                        <span class="badge">5</span>
                    </button>
                    
                    <button class="action-btn" title="الإعدادات">
                        <i class="fas fa-cog"></i>
                    </button>
                </div>

                <!-- User Profile -->
                <div class="user-dropdown">
                    <div class="user-avatar" title="<?php echo e(auth()->user()->name ?? 'المدير'); ?>">
                        <?php echo e(substr(auth()->user()->name ?? 'م', 0, 1)); ?>

                    </div>
                </div>
            </div>
        </nav>

        <!-- Page Content -->
        <main class="page-content">
           

            <?php echo $__env->yieldContent('content'); ?>
        </main>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Sidebar Toggle
            const sidebar = document.getElementById('sidebar');
            const sidebarToggle = document.getElementById('sidebarToggle');
            
            sidebarToggle.addEventListener('click', function() {
                sidebar.classList.toggle('collapsed');
                localStorage.setItem('sidebar-collapsed', sidebar.classList.contains('collapsed'));
            });

            // Restore sidebar state
            if (localStorage.getItem('sidebar-collapsed') === 'true') {
                sidebar.classList.add('collapsed');
            }

            // Loading overlay for navigation
            const navLinks = document.querySelectorAll('.nav-link');
            const loadingOverlay = document.getElementById('loadingOverlay');

            navLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    if (this.getAttribute('href') !== '#' && !this.classList.contains('active')) {
                        loadingOverlay.classList.add('show');
                    }
                });
            });

            // Hide loading on page load
            window.addEventListener('load', function() {
                loadingOverlay.classList.remove('show');
            });

            // Search functionality
            const searchInput = document.querySelector('.search-input');
            searchInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    // Implement search functionality
                    console.log('Searching for:', this.value);
                }
            });

            // Mobile responsive
            function handleResize() {
                if (window.innerWidth <= 768) {
                    sidebar.classList.add('collapsed');
                }
            }

            window.addEventListener('resize', handleResize);
            handleResize();
        });
    </script>

    <?php echo $__env->yieldPushContent('scripts'); ?>
</body>
</html> <?php /**PATH D:\GiftsSaudi-Laravel (1)\resources\views/layouts/admin.blade.php ENDPATH**/ ?>