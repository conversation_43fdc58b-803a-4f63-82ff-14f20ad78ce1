

<?php $__env->startSection('title', 'التقارير المالية للتجار'); ?>

<?php $__env->startSection('content'); ?>
<style>
.financial-card {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    border: 1px solid #ff6b35;
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 20px;
    box-shadow: 0 10px 30px rgba(255, 107, 53, 0.1);
    transition: all 0.3s ease;
}

.financial-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(255, 107, 53, 0.2);
}

.vendor-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #333;
}

.vendor-info {
    display: flex;
    align-items: center;
    gap: 15px;
}

.vendor-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(45deg, #ff6b35, #f7931e);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    font-size: 1.5rem;
}

.vendor-details h5 {
    color: #ff6b35;
    margin-bottom: 5px;
    font-weight: 700;
}

.vendor-details p {
    color: #ccc;
    margin: 0;
    font-size: 0.9rem;
}

.financial-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.stat-item {
    background: rgba(255, 107, 53, 0.1);
    border: 1px solid rgba(255, 107, 53, 0.3);
    border-radius: 10px;
    padding: 15px;
    text-align: center;
}

.stat-number {
    font-size: 1.5rem;
    font-weight: bold;
    color: #ff6b35;
    margin-bottom: 5px;
}

.stat-label {
    color: #ccc;
    font-size: 0.85rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.amount-breakdown {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 10px;
    margin-top: 15px;
}

.amount-item {
    background: rgba(42, 42, 42, 0.8);
    border-radius: 8px;
    padding: 12px;
    text-align: center;
}

.amount-value {
    font-size: 1.2rem;
    font-weight: bold;
    margin-bottom: 3px;
}

.amount-paid {
    color: #28a745;
}

.amount-due {
    color: #ffc107;
}

.amount-commission {
    color: #17a2b8;
}

.filter-section {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    border: 1px solid #333;
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 30px;
}

.btn-gradient {
    background: linear-gradient(45deg, #ff6b35, #f7931e);
    border: none;
    color: white;
    padding: 10px 20px;
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-gradient:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(255, 107, 53, 0.3);
    color: white;
}

.summary-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.summary-card {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    border: 1px solid #ff6b35;
    border-radius: 15px;
    padding: 25px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(255, 107, 53, 0.1);
}

.summary-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(45deg, #ff6b35, #f7931e);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 15px;
    font-size: 1.5rem;
    color: white;
}

.summary-number {
    font-size: 2rem;
    font-weight: bold;
    color: #ff6b35;
    margin-bottom: 10px;
}

.summary-label {
    color: #ccc;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
}
</style>

<div class="page-header">
    <h1 class="page-title">
        <i class="fas fa-chart-line me-2"></i>
        التقارير المالية للتجار
    </h1>
    <div class="page-actions">
        <button onclick="exportReports()" class="btn btn-gradient">
            <i class="fas fa-download me-2"></i>
            تصدير التقرير
        </button>
    </div>
</div>

<!-- Summary Statistics -->
<div class="summary-cards">
    <div class="summary-card">
        <div class="summary-icon">
            <i class="fas fa-store"></i>
        </div>
        <div class="summary-number"><?php echo e(count($vendorReports)); ?></div>
        <div class="summary-label">إجمالي التجار</div>
    </div>
    
    <div class="summary-card">
        <div class="summary-icon">
            <i class="fas fa-coins"></i>
        </div>
        <div class="summary-number"><?php echo e(number_format(array_sum(array_column($vendorReports, 'amount_paid')), 2)); ?></div>
        <div class="summary-label">إجمالي المدفوعات (ريال)</div>
    </div>
    
    <div class="summary-card">
        <div class="summary-icon">
            <i class="fas fa-hand-holding-usd"></i>
        </div>
        <div class="summary-number"><?php echo e(number_format(array_sum(array_column($vendorReports, 'total_commission')), 2)); ?></div>
        <div class="summary-label">إجمالي العمولات (ريال)</div>
    </div>
    
    <div class="summary-card">
        <div class="summary-icon">
            <i class="fas fa-envelope"></i>
        </div>
        <div class="summary-number"><?php echo e(number_format(array_sum(array_column($vendorReports, 'total_messages_sent')))); ?></div>
        <div class="summary-label">إجمالي الرسائل المرسلة</div>
    </div>
</div>

<!-- Filters -->
<div class="filter-section">
    <h4 class="text-white mb-3">
        <i class="fas fa-filter me-2"></i>
        تصفية التقارير
    </h4>
    
    <form method="GET" action="<?php echo e(route('admin.campaigns.financial-reports')); ?>" class="row g-3">
        <div class="col-md-3">
            <label class="form-label text-white">التاجر</label>
            <select name="vendor_id" class="form-select">
                <option value="">جميع التجار</option>
                <?php $__currentLoopData = $vendors; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $vendor): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <option value="<?php echo e($vendor->id); ?>" <?php echo e(request('vendor_id') == $vendor->id ? 'selected' : ''); ?>>
                        <?php echo e($vendor->company_name); ?>

                    </option>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </select>
        </div>
        
        <div class="col-md-2">
            <label class="form-label text-white">من تاريخ</label>
            <input type="date" name="date_from" class="form-control" value="<?php echo e(request('date_from')); ?>">
        </div>
        
        <div class="col-md-2">
            <label class="form-label text-white">إلى تاريخ</label>
            <input type="date" name="date_to" class="form-control" value="<?php echo e(request('date_to')); ?>">
        </div>
        
        <div class="col-md-3">
            <label class="form-label text-white">حالة الحملة</label>
            <select name="status" class="form-select">
                <option value="">جميع الحالات</option>
                <option value="completed" <?php echo e(request('status') == 'completed' ? 'selected' : ''); ?>>مكتملة</option>
                <option value="processing" <?php echo e(request('status') == 'processing' ? 'selected' : ''); ?>>قيد التنفيذ</option>
                <option value="approved" <?php echo e(request('status') == 'approved' ? 'selected' : ''); ?>>معتمدة</option>
                <option value="pending_approval" <?php echo e(request('status') == 'pending_approval' ? 'selected' : ''); ?>>في انتظار الاعتماد</option>
            </select>
        </div>
        
        <div class="col-md-2">
            <label class="form-label">&nbsp;</label>
            <div class="d-grid">
                <button type="submit" class="btn btn-gradient">
                    <i class="fas fa-search me-2"></i>
                    بحث
                </button>
            </div>
        </div>
    </form>
</div>

<!-- Vendor Financial Reports -->
<div class="row">
    <?php $__empty_1 = true; $__currentLoopData = $vendorReports; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $vendorId => $report): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
        <div class="col-12 mb-4">
            <div class="financial-card">
                <div class="vendor-header">
                    <div class="vendor-info">
                        <div class="vendor-avatar">
                            <?php echo e(mb_substr($report['vendor']->company_name, 0, 1)); ?>

                        </div>
                        <div class="vendor-details">
                            <h5><?php echo e($report['vendor']->company_name); ?></h5>
                            <p><?php echo e($report['vendor']->contact_person); ?> - <?php echo e($report['vendor']->phone); ?></p>
                            <p><?php echo e($report['campaigns_count']); ?> حملة إعلانية</p>
                        </div>
                    </div>
                    <div class="text-end">
                        <a href="<?php echo e(route('admin.campaigns.vendor-financial-details', $report['vendor']->id)); ?>" 
                           class="btn btn-gradient btn-sm">
                            <i class="fas fa-eye me-1"></i>
                            التفاصيل الكاملة
                        </a>
                    </div>
                </div>

                <div class="financial-stats">
                    <div class="stat-item">
                        <div class="stat-number"><?php echo e(number_format($report['total_messages_sent'])); ?></div>
                        <div class="stat-label">الرسائل المرسلة</div>
                    </div>
                    
                    <div class="stat-item">
                        <div class="stat-number"><?php echo e(number_format($report['total_gifts_claimed'])); ?></div>
                        <div class="stat-label">الهدايا المستلمة</div>
                    </div>
                    
                    <div class="stat-item">
                        <div class="stat-number">%<?php echo e($report['commission_rate']); ?></div>
                        <div class="stat-label">نسبة العمولة</div>
                    </div>
                    
                    <div class="stat-item">
                        <div class="stat-number"><?php echo e(number_format($report['total_campaign_cost'], 2)); ?></div>
                        <div class="stat-label">تكلفة الحملات (ريال)</div>
                    </div>
                </div>

                <div class="amount-breakdown">
                    <div class="amount-item">
                        <div class="amount-value amount-paid"><?php echo e(number_format($report['amount_paid'], 2)); ?> ريال</div>
                        <div class="stat-label">المبلغ المدفوع</div>
                    </div>
                    
                    <div class="amount-item">
                        <div class="amount-value amount-commission"><?php echo e(number_format($report['total_commission'], 2)); ?> ريال</div>
                        <div class="stat-label">إجمالي العمولات</div>
                    </div>
                    
                    <div class="amount-item">
                        <div class="amount-value amount-due"><?php echo e(number_format($report['amount_due'], 2)); ?> ريال</div>
                        <div class="stat-label">المبلغ المستحق</div>
                    </div>
                    
                    <div class="amount-item">
                        <div class="amount-value <?php echo e($report['amount_paid'] - $report['total_commission'] >= 0 ? 'amount-paid' : 'amount-due'); ?>">
                            <?php echo e(number_format($report['amount_paid'] - $report['total_commission'], 2)); ?> ريال
                        </div>
                        <div class="stat-label">صافي الربح/الخسارة</div>
                    </div>
                </div>
            </div>
        </div>
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
        <div class="col-12">
            <div class="text-center py-5">
                <i class="fas fa-chart-line fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">لا توجد تقارير مالية</h5>
                <p class="text-muted">لا توجد بيانات مالية متاحة للفترة المحددة.</p>
            </div>
        </div>
    <?php endif; ?>
</div>

<!-- Pagination -->
<?php if($campaigns->hasPages()): ?>
    <div class="d-flex justify-content-center mt-4">
        <?php echo e($campaigns->appends(request()->query())->links()); ?>

    </div>
<?php endif; ?>

<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
function exportReports() {
    // Create a form to export the data
    const form = document.createElement('form');
    form.method = 'GET';
    form.action = '<?php echo e(route("admin.campaigns.financial-reports")); ?>';
    
    // Add current filter parameters
    const params = new URLSearchParams(window.location.search);
    params.append('export', 'excel');
    
    for (const [key, value] of params) {
        const input = document.createElement('input');
        input.type = 'hidden';
        input.name = key;
        input.value = value;
        form.appendChild(input);
    }
    
    document.body.appendChild(form);
    form.submit();
    document.body.removeChild(form);
}

// Auto-submit form when filters change
document.addEventListener('DOMContentLoaded', function() {
    const filterForm = document.querySelector('form');
    const selects = filterForm.querySelectorAll('select');
    const inputs = filterForm.querySelectorAll('input[type="date"]');
    
    [...selects, ...inputs].forEach(element => {
        element.addEventListener('change', function() {
            // Auto submit after 500ms delay
            setTimeout(() => {
                filterForm.submit();
            }, 500);
        });
    });
});
</script>
<?php $__env->stopPush(); ?> 
<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\GiftsSaudi-Laravel (1)\resources\views\admin\campaigns\financial-reports.blade.php ENDPATH**/ ?>