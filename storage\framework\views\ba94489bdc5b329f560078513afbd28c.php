

<?php $__env->startSection('title', 'إضافة هدية جديدة'); ?>

<?php $__env->startSection('page-title', 'إضافة هدية جديدة'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="create-form-container">
                <div class="create-form-header">
                    <h1>
                        <i class="fas fa-gift me-2"></i>
                        إضافة هدية جديدة
                    </h1>
                    <p>أضف هدية جديدة إلى متجرك، ستحتاج موافقة الإدارة قبل النشر</p>
                </div>
                
                <div class="create-form-body">
                    <?php if(session('success')): ?>
                        <div class="alert alert-success">
                            <?php echo e(session('success')); ?>

                        </div>
                    <?php endif; ?>

                    <form action="<?php echo e(route('vendor.gifts.store')); ?>" method="POST" enctype="multipart/form-data">
                        <?php echo csrf_field(); ?>

                        <!-- Basic Information Section -->
                        <div class="form-section">
                            <h3 class="form-section-title">
                                <i class="fas fa-info-circle"></i>
                                المعلومات الأساسية
                            </h3>

                            <div class="form-group">
                                <label for="name" class="form-label">
                                    <i class="fas fa-tag"></i>
                                    اسم الهدية *
                                </label>
                                <input type="text" 
                                       id="name" 
                                       name="name" 
                                       class="form-control <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                       value="<?php echo e(old('name')); ?>" 
                                       placeholder="أدخل اسم الهدية"
                                       required>
                                <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>

                            <div class="form-group">
                                <label for="description" class="form-label">
                                    <i class="fas fa-align-left"></i>
                                    وصف الهدية *
                                </label>
                                <textarea id="description" 
                                          name="description" 
                                          class="form-control <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                          rows="4" 
                                          placeholder="أضف وصفاً تفصيلياً للهدية"
                                          required><?php echo e(old('description')); ?></textarea>
                                <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>

                            <div class="form-group">
                                <label for="category" class="form-label">
                                    <i class="fas fa-list"></i>
                                    الفئة *
                                </label>
                                <select id="category" 
                                        name="category" 
                                        class="form-select <?php $__errorArgs = ['category'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                        required>
                                    <option value="">اختر الفئة</option>
                                    <option value="electronics" <?php echo e(old('category') == 'electronics' ? 'selected' : ''); ?>>إلكترونيات</option>
                                    <option value="fashion" <?php echo e(old('category') == 'fashion' ? 'selected' : ''); ?>>أزياء وملابس</option>
                                    <option value="home" <?php echo e(old('category') == 'home' ? 'selected' : ''); ?>>منزل ومطبخ</option>
                                    <option value="beauty" <?php echo e(old('category') == 'beauty' ? 'selected' : ''); ?>>جمال وعناية</option>
                                    <option value="sports" <?php echo e(old('category') == 'sports' ? 'selected' : ''); ?>>رياضة وصحة</option>
                                    <option value="books" <?php echo e(old('category') == 'books' ? 'selected' : ''); ?>>كتب وثقافة</option>
                                    <option value="toys" <?php echo e(old('category') == 'toys' ? 'selected' : ''); ?>>ألعاب وترفيه</option>
                                    <option value="food" <?php echo e(old('category') == 'food' ? 'selected' : ''); ?>>طعام ومشروبات</option>
                                    <option value="other" <?php echo e(old('category') == 'other' ? 'selected' : ''); ?>>أخرى</option>
                                </select>
                                <?php $__errorArgs = ['category'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>

                        <!-- Pricing & Stock Section -->
                        <div class="form-section">
                            <h3 class="form-section-title">
                                <i class="fas fa-money-bill-wave"></i>
                                التسعير والمخزون
                            </h3>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="price" class="form-label">
                                            <i class="fas fa-dollar-sign"></i>
                                            السعر (ريال) *
                                        </label>
                                        <input type="number" 
                                               id="price" 
                                               name="price" 
                                               class="form-control <?php $__errorArgs = ['price'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                               value="<?php echo e(old('price')); ?>" 
                                               placeholder="0.00"
                                               step="0.01"
                                               min="0"
                                               required>
                                        <?php $__errorArgs = ['price'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="stock_quantity" class="form-label">
                                            <i class="fas fa-boxes"></i>
                                            الكمية المتوفرة *
                                        </label>
                                        <input type="number" 
                                               id="stock_quantity" 
                                               name="stock_quantity" 
                                               class="form-control <?php $__errorArgs = ['stock_quantity'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                               value="<?php echo e(old('stock_quantity')); ?>" 
                                               placeholder="0"
                                               min="0"
                                               required>
                                        <?php $__errorArgs = ['stock_quantity'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Image Section -->
                        <div class="form-section">
                            <h3 class="form-section-title">
                                <i class="fas fa-image"></i>
                                صورة الهدية
                            </h3>

                            <div class="form-group">
                                <label for="image" class="form-label">
                                    <i class="fas fa-camera"></i>
                                    اختر صورة الهدية
                                </label>
                                <input type="file" 
                                       id="image" 
                                       name="image" 
                                       class="form-control <?php $__errorArgs = ['image'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                       accept="image/*"
                                       onchange="previewImage(this)">
                                <?php $__errorArgs = ['image'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                
                                <div id="imagePreview" style="display: none; margin-top: 15px; text-align: center;">
                                    <img id="previewImg" src="" alt="معاينة الصورة" style="max-width: 200px; max-height: 200px; border-radius: 15px; border: 2px solid var(--vendor-secondary);">
                                </div>
                            </div>
                        </div>

                        <!-- Form Actions -->
                        <div class="d-flex gap-3 mt-4">
                            <a href="<?php echo e(route('vendor.gifts.index')); ?>" class="btn btn-secondary">
                                <i class="fas fa-times"></i>
                                إلغاء
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i>
                                حفظ الهدية
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
function previewImage(input) {
    const preview = document.getElementById('imagePreview');
    const previewImg = document.getElementById('previewImg');
    
    if (input.files && input.files[0]) {
        const reader = new FileReader();
        
        reader.onload = function(e) {
            previewImg.src = e.target.result;
            preview.style.display = 'block';
        }
        
        reader.readAsDataURL(input.files[0]);
    } else {
        preview.style.display = 'none';
    }
}

// Form validation
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('form');
    const inputs = form.querySelectorAll('input, textarea, select');
    
    inputs.forEach(input => {
        input.addEventListener('blur', function() {
            validateField(this);
        });
    });
    
    form.addEventListener('submit', function(e) {
        let isValid = true;
        inputs.forEach(input => {
            if (input.hasAttribute('required') && !input.value.trim()) {
                isValid = false;
                input.classList.add('is-invalid');
            }
        });
        
        if (!isValid) {
            e.preventDefault();
        }
    });
});

function validateField(field) {
    if (field.hasAttribute('required') && !field.value.trim()) {
        field.classList.add('is-invalid');
    } else {
        field.classList.remove('is-invalid');
    }
}
</script>
<?php $__env->stopPush(); ?> 
<?php echo $__env->make('layouts.vendor', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\GiftsSaudi-Laravel (1)\resources\views/vendor/gifts/create.blade.php ENDPATH**/ ?>